import { Link } from 'react-router-dom';
import { Building2, User, ArrowR<PERSON> } from 'lucide-react';

const LoginChoice = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="max-w-4xl mx-auto px-6">
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold text-gray-800 mb-4">TaskSphere</h1>
          <p className="text-xl text-gray-600 mb-8">
            Multi-tenant task management platform
          </p>
          <p className="text-lg text-gray-500">
            Choose your portal to get started
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-3xl mx-auto">
          {/* Organization Portal */}
          <Link
            to="/organization/login"
            className="group block p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border-2 border-transparent hover:border-blue-200"
          >
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6 group-hover:bg-blue-200 transition-colors">
                <Building2 className="h-8 w-8 text-blue-600" />
              </div>
              
              <h2 className="text-2xl font-bold text-gray-800 mb-4">
                Organization Portal
              </h2>
              
              <p className="text-gray-600 mb-6 leading-relaxed">
                For organization administrators to manage teams, create tasks, 
                and oversee company operations.
              </p>
              
              <div className="space-y-3 text-sm text-gray-500 mb-6">
                <div className="flex items-center justify-center">
                  <span>✓ Manage teams and groups</span>
                </div>
                <div className="flex items-center justify-center">
                  <span>✓ Create and assign tasks</span>
                </div>
                <div className="flex items-center justify-center">
                  <span>✓ Approve task swaps</span>
                </div>
                <div className="flex items-center justify-center">
                  <span>✓ View analytics and reports</span>
                </div>
              </div>
              
              <div className="inline-flex items-center text-blue-600 font-semibold group-hover:text-blue-700">
                Sign in as Admin
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </div>
            </div>
          </Link>

          {/* User Portal */}
          <Link
            to="/user/login"
            className="group block p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border-2 border-transparent hover:border-green-200"
          >
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-6 group-hover:bg-green-200 transition-colors">
                <User className="h-8 w-8 text-green-600" />
              </div>
              
              <h2 className="text-2xl font-bold text-gray-800 mb-4">
                User Portal
              </h2>
              
              <p className="text-gray-600 mb-6 leading-relaxed">
                For team members to view assigned tasks, manage workload, 
                and collaborate with colleagues.
              </p>
              
              <div className="space-y-3 text-sm text-gray-500 mb-6">
                <div className="flex items-center justify-center">
                  <span>✓ View assigned tasks</span>
                </div>
                <div className="flex items-center justify-center">
                  <span>✓ Update task progress</span>
                </div>
                <div className="flex items-center justify-center">
                  <span>✓ Request task swaps</span>
                </div>
                <div className="flex items-center justify-center">
                  <span>✓ Receive notifications</span>
                </div>
              </div>
              
              <div className="inline-flex items-center text-green-600 font-semibold group-hover:text-green-700">
                Sign in as User
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </div>
            </div>
          </Link>
        </div>

        {/* Demo Information */}
        <div className="mt-12 text-center">
          <div className="inline-block p-6 bg-white rounded-xl shadow-sm border border-gray-200">
            <h3 className="font-semibold text-gray-800 mb-4">Demo Credentials</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
              <div>
                <h4 className="font-medium text-blue-600 mb-2">Organization Admin</h4>
                <p className="text-gray-600">Email: <EMAIL></p>
                <p className="text-gray-600">Password: admin123</p>
              </div>
              <div>
                <h4 className="font-medium text-green-600 mb-2">User Account</h4>
                <p className="text-gray-600">Email: <EMAIL></p>
                <p className="text-gray-600">Password: user123</p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-12 text-center text-gray-500 text-sm">
          <p>
            New to TaskSphere?{' '}
            <Link to="/organization/register" className="text-blue-600 hover:text-blue-700">
              Create an organization
            </Link>
            {' '}or{' '}
            <Link to="/user/register" className="text-green-600 hover:text-green-700">
              join as a user
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginChoice;
