from django.db import models
from django.conf import settings


class Notification(models.Model):
    """Notification model for user notifications"""
    TYPE_CHOICES = [
        ('task_assigned', 'Task Assigned'),
        ('task_deadline', 'Task Deadline Approaching'),
        ('swap_request', 'Swap Request Received'),
        ('swap_admin_approval', 'Swap Request Pending Admin Approval'),
        ('swap_approved', 'Swap Request Approved'),
        ('swap_rejected', 'Swap Request Rejected'),
        ('task_overdue', 'Task Overdue'),
        ('task_completed', 'Task Completed'),
    ]

    recipient = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='notifications'
    )
    title = models.Char<PERSON>ield(max_length=200)
    message = models.TextField()
    notification_type = models.Char<PERSON>ield(max_length=20, choices=TYPE_CHOICES)
    is_read = models.Bo<PERSON>anField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)

    # Optional references to related objects
    related_task = models.ForeignKey(
        'core.Task',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='notifications'
    )
    related_swap = models.ForeignKey(
        'core.TaskSwap',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='notifications'
    )

    def __str__(self):
        return f"{self.title} - {self.recipient.user_id}"

    class Meta:
        ordering = ['-created_at']
