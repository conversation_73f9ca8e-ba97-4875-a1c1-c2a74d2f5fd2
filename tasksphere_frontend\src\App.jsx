
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';

import { AuthProvider } from './context/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import AdminLayout from './components/layout/AdminLayout';
import UserLayout from './components/layout/UserLayout';
import LoginChoice from './components/auth/LoginChoice';
import OrganizationLogin from './components/auth/OrganizationLogin';
import UserLogin from './components/auth/UserLogin';
import Register from './components/auth/Register';
import AdminDashboard from './components/dashboard/AdminDashboard';
import UserDashboard from './components/dashboard/UserDashboard';
import TaskList from './components/tasks/TaskList';
import TaskForm from './components/tasks/TaskForm';
import TaskSwapList from './components/swaps/TaskSwapList';
import GroupManagement from './components/groups/GroupManagement';
import NotificationList from './components/notifications/NotificationList';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

// Placeholder components for routes that will be implemented later
const Profile = () => (
  <div className="text-center py-12">
    <h1 className="text-2xl font-bold mb-4">Profile</h1>
    <p className="text-base-content/70">Profile management interface coming soon...</p>
  </div>
);

const Analytics = () => (
  <div className="text-center py-12">
    <h1 className="text-2xl font-bold mb-4">Analytics</h1>
    <p className="text-base-content/70">Analytics dashboard coming soon...</p>
  </div>
);

const Settings = () => (
  <div className="text-center py-12">
    <h1 className="text-2xl font-bold mb-4">Settings</h1>
    <p className="text-base-content/70">Settings panel coming soon...</p>
  </div>
);

const Calendar = () => (
  <div className="text-center py-12">
    <h1 className="text-2xl font-bold mb-4">Calendar</h1>
    <p className="text-base-content/70">Calendar view coming soon...</p>
  </div>
);

const Messages = () => (
  <div className="text-center py-12">
    <h1 className="text-2xl font-bold mb-4">Messages</h1>
    <p className="text-base-content/70">Messages interface coming soon...</p>
  </div>
);

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <Router>
          <div className="App">
            <Routes>
              {/* Landing page */}
              <Route path="/" element={<LoginChoice />} />

              {/* Organization routes */}
              <Route path="/organization/login" element={<OrganizationLogin />} />
              <Route path="/organization/register" element={<Register />} />

              {/* User routes */}
              <Route path="/user/login" element={<UserLogin />} />
              <Route path="/user/register" element={<Register />} />

              {/* Admin protected routes */}
              <Route
                path="/admin"
                element={
                  <ProtectedRoute requireAdmin={true}>
                    <AdminLayout />
                  </ProtectedRoute>
                }
              >
                <Route index element={<Navigate to="/admin/dashboard" replace />} />
                <Route path="dashboard" element={<AdminDashboard />} />
                <Route path="tasks" element={<TaskList />} />
                <Route path="tasks/new" element={<TaskForm />} />
                <Route path="tasks/edit/:id" element={<TaskForm />} />
                <Route path="groups" element={<GroupManagement />} />
                <Route path="swaps" element={<TaskSwapList />} />
                <Route path="analytics" element={<Analytics />} />
                <Route path="settings" element={<Settings />} />
                <Route path="notifications" element={<NotificationList />} />
                <Route path="profile" element={<Profile />} />
              </Route>

              {/* User protected routes */}
              <Route
                path="/user"
                element={
                  <ProtectedRoute>
                    <UserLayout />
                  </ProtectedRoute>
                }
              >
                <Route index element={<Navigate to="/user/dashboard" replace />} />
                <Route path="dashboard" element={<UserDashboard />} />
                <Route path="tasks" element={<TaskList />} />
                <Route path="swaps" element={<TaskSwapList />} />
                <Route path="calendar" element={<Calendar />} />
                <Route path="messages" element={<Messages />} />
                <Route path="notifications" element={<NotificationList />} />
                <Route path="profile" element={<Profile />} />
              </Route>

              {/* Legacy redirects */}
              <Route path="/login" element={<Navigate to="/" replace />} />
              <Route path="/register" element={<Navigate to="/" replace />} />
              <Route path="/dashboard" element={<Navigate to="/" replace />} />

              {/* Catch all route */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>

            {/* Toast notifications */}
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: 'hsl(var(--b1))',
                  color: 'hsl(var(--bc))',
                  border: '1px solid hsl(var(--b3))',
                },
              }}
            />
          </div>
        </Router>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
