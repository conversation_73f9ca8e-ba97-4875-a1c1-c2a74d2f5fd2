from django.contrib.auth.models import AbstractUser
from django.db import models
import string
import random


def generate_user_id():
    """Generate a unique 8-digit alphanumeric User ID"""
    return ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))


class User(AbstractUser):
    """Custom User model with 8-digit alphanumeric User ID"""
    user_id = models.CharField(
        max_length=8,
        unique=True,
        default=generate_user_id,
        help_text="8-digit alphanumeric User ID"
    )
    email = models.EmailField(unique=True)
    first_name = models.Char<PERSON>ield(max_length=30)
    last_name = models.Char<PERSON><PERSON>(max_length=30)
    phone_number = models.CharField(max_length=15, blank=True, null=True)
    date_joined = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    # Override username to use email
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'first_name', 'last_name']

    def save(self, *args, **kwargs):
        # Ensure user_id is unique
        while User.objects.filter(user_id=self.user_id).exists():
            self.user_id = generate_user_id()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.user_id})"

    class Meta:
        db_table = 'auth_user'
