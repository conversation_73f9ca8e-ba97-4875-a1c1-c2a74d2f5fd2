#!/usr/bin/env python
"""
Test script to verify group creation API endpoint
"""

import requests
import json

# API base URL
BASE_URL = "http://127.0.0.1:8000/api"

def test_group_creation():
    print("Testing Group Creation API...")
    
    # Step 1: Login as admin
    print("\n1. Logging in as admin...")
    login_data = {
        "email": "<EMAIL>",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login/", json=login_data)
    
    if response.status_code != 200:
        print(f"❌ Login failed: {response.status_code}")
        print(response.text)
        return
    
    login_result = response.json()
    access_token = login_result['tokens']['access']
    user_data = login_result['user']
    
    print(f"✅ Login successful!")
    print(f"User: {user_data['first_name']} {user_data['last_name']}")
    print(f"Companies: {len(user_data['companies'])}")
    
    if not user_data['companies']:
        print("❌ No companies found for user")
        return
    
    company_id = user_data['companies'][0]['company_id']
    print(f"Company ID: {company_id}")
    
    # Step 2: Test group creation
    print("\n2. Creating a test group...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    group_data = {
        "name": "Test API Group",
        "description": "Group created via API test",
        "company": company_id
    }
    
    print(f"Group data: {json.dumps(group_data, indent=2)}")
    
    response = requests.post(f"{BASE_URL}/groups/", json=group_data, headers=headers)
    
    print(f"Response status: {response.status_code}")
    print(f"Response headers: {dict(response.headers)}")
    print(f"Response body: {response.text}")
    
    if response.status_code == 201:
        print("✅ Group created successfully!")
        group_result = response.json()
        print(f"Created group: {group_result}")
    else:
        print(f"❌ Group creation failed: {response.status_code}")
        try:
            error_data = response.json()
            print(f"Error details: {json.dumps(error_data, indent=2)}")
        except:
            print(f"Raw error: {response.text}")
    
    # Step 3: List groups to verify
    print("\n3. Listing groups...")
    
    response = requests.get(f"{BASE_URL}/groups/?company_id={company_id}", headers=headers)
    
    if response.status_code == 200:
        groups = response.json()
        print(f"✅ Found {len(groups)} groups")
        for group in groups:
            print(f"  - {group['name']} (ID: {group['id']})")
    else:
        print(f"❌ Failed to list groups: {response.status_code}")
        print(response.text)

if __name__ == "__main__":
    test_group_creation()
