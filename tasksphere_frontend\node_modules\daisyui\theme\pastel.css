:root:has(input.theme-controller[value=pastel]:checked),[data-theme="pastel"] {
color-scheme: light;
--color-base-100: oklch(100% 0 0);
--color-base-200: oklch(98.462% 0.001 247.838);
--color-base-300: oklch(92.462% 0.001 247.838);
--color-base-content: oklch(20% 0 0);
--color-primary: oklch(90% 0.063 306.703);
--color-primary-content: oklch(49% 0.265 301.924);
--color-secondary: oklch(89% 0.058 10.001);
--color-secondary-content: oklch(51% 0.222 16.935);
--color-accent: oklch(90% 0.093 164.15);
--color-accent-content: oklch(50% 0.118 165.612);
--color-neutral: oklch(55% 0.046 257.417);
--color-neutral-content: oklch(92% 0.013 255.508);
--color-info: oklch(86% 0.127 207.078);
--color-info-content: oklch(52% 0.105 223.128);
--color-success: oklch(87% 0.15 154.449);
--color-success-content: oklch(52% 0.154 150.069);
--color-warning: oklch(83% 0.128 66.29);
--color-warning-content: oklch(55% 0.195 38.402);
--color-error: oklch(80% 0.114 19.571);
--color-error-content: oklch(50% 0.213 27.518);
--radius-selector: 1rem;
--radius-field: 2rem;
--radius-box: 1rem;
--size-selector: 0.25rem;
--size-field: 0.25rem;
--border: 2px;
--depth: 0;
--noise: 0;
}
