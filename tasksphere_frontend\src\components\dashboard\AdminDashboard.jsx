import { useQuery } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { tasksAPI, taskSwapsAPI, notificationsAPI, groupsAPI } from '../../services/api';
import {
  Users,
  CheckSquare,
  ArrowRightLeft,
  Bell,
  Plus,
  TrendingUp,
  Building2,
  Shield,
  BarChart3,
} from 'lucide-react';

const AdminDashboard = () => {
  const { user, getPrimaryCompany } = useAuth();
  const primaryCompany = getPrimaryCompany();

  // Fetch admin dashboard data
  const { data: allTasksResponse = [] } = useQuery({
    queryKey: ['admin-tasks', primaryCompany?.company_id],
    queryFn: () =>
      tasksAPI.getTasks({
        company_id: primaryCompany?.company_id,
      }),
    enabled: !!primaryCompany?.company_id,
  });

  const { data: groupsResponse = [] } = useQuery({
    queryKey: ['admin-groups', primaryCompany?.company_id],
    queryFn: () => groupsAPI.getGroups(primaryCompany?.company_id),
    enabled: !!primaryCompany?.company_id,
  });

  const { data: taskSwapsResponse = [] } = useQuery({
    queryKey: ['admin-taskSwaps', primaryCompany?.company_id],
    queryFn: () =>
      taskSwapsAPI.getTaskSwaps({
        company_id: primaryCompany?.company_id,
      }),
    enabled: !!primaryCompany?.company_id,
  });

  // Handle paginated responses
  const allTasks = Array.isArray(allTasksResponse) ? allTasksResponse : (allTasksResponse?.results || []);
  const groups = Array.isArray(groupsResponse) ? groupsResponse : (groupsResponse?.results || []);
  const taskSwaps = Array.isArray(taskSwapsResponse) ? taskSwapsResponse : (taskSwapsResponse?.results || []);

  // Calculate admin statistics
  const adminStats = {
    totalTasks: allTasks.length,
    pendingTasks: allTasks.filter((task) => task.status === 'pending').length,
    completedTasks: allTasks.filter((task) => task.status === 'completed').length,
    overdueTasks: allTasks.filter((task) => task.is_overdue).length,
    totalGroups: groups.length,
    pendingSwaps: taskSwaps.filter((swap) => swap.status === 'pending_admin').length,
    approvedSwaps: taskSwaps.filter((swap) => swap.status === 'approved').length,
  };

  const StatCard = ({ title, value, icon: Icon, color, link, description }) => (
    <div className="card bg-white shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
      <div className="card-body p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-gray-600 text-sm font-medium">{title}</p>
            <p className={`text-3xl font-bold ${color} mt-1`}>{value}</p>
            {description && (
              <p className="text-xs text-gray-500 mt-1">{description}</p>
            )}
          </div>
          <div className={`p-3 rounded-lg ${color} bg-opacity-10`}>
            <Icon className={`h-8 w-8 ${color}`} />
          </div>
        </div>
        {link && (
          <Link to={link} className="btn btn-outline btn-sm mt-4 self-start">
            Manage
          </Link>
        )}
      </div>
    </div>
  );

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold text-gray-800 flex items-center">
            <Shield className="h-8 w-8 mr-3 text-blue-600" />
            Admin Dashboard
          </h1>
          <p className="text-gray-600 mt-2 text-lg">
            Welcome back, {user?.first_name}! Manage your organization efficiently.
          </p>
          {primaryCompany && (
            <div className="mt-3">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                <Building2 className="h-4 w-4 mr-1" />
                {primaryCompany.company_name} ({primaryCompany.company_code})
              </span>
            </div>
          )}
        </div>
        <div className="flex space-x-3">
          <Link to="/admin/tasks/new" className="btn btn-primary">
            <Plus className="h-5 w-5 mr-2" />
            Create Task
          </Link>
          <Link to="/admin/groups" className="btn btn-outline">
            <Users className="h-5 w-5 mr-2" />
            Manage Groups
          </Link>
        </div>
      </div>

      {/* Admin Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Tasks"
          value={adminStats.totalTasks}
          icon={CheckSquare}
          color="text-blue-600"
          link="/admin/tasks"
          description="All tasks in organization"
        />
        <StatCard
          title="Pending Tasks"
          value={adminStats.pendingTasks}
          icon={TrendingUp}
          color="text-yellow-600"
          description="Awaiting assignment"
        />
        <StatCard
          title="Groups"
          value={adminStats.totalGroups}
          icon={Users}
          color="text-green-600"
          link="/admin/groups"
          description="Active teams"
        />
        <StatCard
          title="Pending Swaps"
          value={adminStats.pendingSwaps}
          icon={ArrowRightLeft}
          color="text-purple-600"
          link="/admin/swaps"
          description="Awaiting approval"
        />
      </div>

      {/* Management Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Tasks */}
        <div className="card bg-white shadow-sm border border-gray-200">
          <div className="card-body">
            <div className="flex items-center justify-between mb-6">
              <h2 className="card-title text-xl text-gray-800">Recent Tasks</h2>
              <Link to="/admin/tasks" className="btn btn-ghost btn-sm text-blue-600">
                View All
              </Link>
            </div>
            <div className="space-y-4">
              {allTasks.slice(0, 5).map((task) => (
                <div
                  key={task.id}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
                >
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-800">{task.title}</h3>
                    <p className="text-sm text-gray-600">
                      Assigned to: {task.assigned_to_name || task.assigned_group_name || 'Unassigned'}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span
                      className={`badge badge-sm ${
                        task.status === 'completed'
                          ? 'badge-success'
                          : task.status === 'in_progress'
                          ? 'badge-warning'
                          : task.is_overdue
                          ? 'badge-error'
                          : 'badge-ghost'
                      }`}
                    >
                      {task.status.replace('_', ' ')}
                    </span>
                  </div>
                </div>
              ))}
              {allTasks.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <CheckSquare className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>No tasks created yet.</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Pending Approvals */}
        <div className="card bg-white shadow-sm border border-gray-200">
          <div className="card-body">
            <div className="flex items-center justify-between mb-6">
              <h2 className="card-title text-xl text-gray-800">Pending Approvals</h2>
              <Link to="/admin/swaps" className="btn btn-ghost btn-sm text-purple-600">
                View All
              </Link>
            </div>
            <div className="space-y-4">
              {taskSwaps
                .filter((swap) => swap.status === 'pending_admin')
                .slice(0, 5)
                .map((swap) => (
                  <div
                    key={swap.id}
                    className="flex items-center justify-between p-4 bg-purple-50 rounded-lg"
                  >
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-800">{swap.task_title}</h3>
                      <p className="text-sm text-gray-600">
                        {swap.requested_by_name} → {swap.requested_to_name}
                      </p>
                    </div>
                    <span className="badge badge-warning badge-sm">
                      Pending
                    </span>
                  </div>
                ))}
              {taskSwaps.filter((swap) => swap.status === 'pending_admin').length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <ArrowRightLeft className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>No pending approvals.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card bg-white shadow-sm border border-gray-200">
        <div className="card-body">
          <h2 className="card-title text-xl text-gray-800 mb-6">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link to="/admin/tasks/new" className="btn btn-outline btn-lg">
              <CheckSquare className="h-6 w-6 mr-2" />
              Create Task
            </Link>
            <Link to="/admin/groups" className="btn btn-outline btn-lg">
              <Users className="h-6 w-6 mr-2" />
              Manage Groups
            </Link>
            <Link to="/admin/swaps" className="btn btn-outline btn-lg">
              <ArrowRightLeft className="h-6 w-6 mr-2" />
              Review Swaps
            </Link>
            <Link to="/admin/analytics" className="btn btn-outline btn-lg">
              <BarChart3 className="h-6 w-6 mr-2" />
              View Analytics
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
