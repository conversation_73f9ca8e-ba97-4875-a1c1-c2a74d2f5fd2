/*! 🌼 daisyUI 5.0.45 - MIT License */ @layer utilities{.toggle{border:var(--border)solid currentColor;color:var(--input-color);cursor:pointer;appearance:none;vertical-align:middle;-webkit-user-select:none;user-select:none;--radius-selector-max:calc(var(--radius-selector) + var(--radius-selector) + var(--radius-selector));border-radius:calc(var(--radius-selector) + min(var(--toggle-p),var(--radius-selector-max)) + min(var(--border),var(--radius-selector-max)));padding:var(--toggle-p);box-shadow:0 1px color-mix(in oklab,currentColor calc(var(--depth)*10%),#0000)inset;--input-color:color-mix(in oklab,var(--color-base-content)50%,#0000);--toggle-p:calc(var(--size)*.125);--size:calc(var(--size-selector,.25rem)*6);width:calc((var(--size)*2) - (var(--border) + var(--toggle-p))*2);height:var(--size);flex-shrink:0;grid-template-columns:0fr 1fr 1fr;place-content:center;transition:color .3s,grid-template-columns .2s;display:inline-grid;position:relative;&>*{z-index:1;cursor:pointer;appearance:none;background-color:#0000;border:none;grid-column:2/span 1;grid-row-start:1;height:100%;padding:.125rem;transition:opacity .2s,rotate .4s;&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:nth-child(2){color:var(--color-base-100);rotate:none}&:nth-child(3){color:var(--color-base-100);opacity:0;rotate:-15deg}}&:has(:checked){&>:nth-child(2){opacity:0;rotate:15deg}&>:nth-child(3){opacity:1;rotate:none}}&:before{aspect-ratio:1;border-radius:var(--radius-selector);--tw-content:"";content:var(--tw-content);height:100%;box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1))inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px color-mix(in oklab,currentColor calc(var(--depth)*10%),#0000);background-color:currentColor;background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);grid-row-start:1;grid-column-start:2;transition:background-color .1s,translate .2s,inset-inline-start .2s;position:relative;inset-inline-start:0;translate:0}@media (forced-colors:active){&:before{outline-style:var(--tw-outline-style);outline-offset:calc(1px*-1);outline-width:1px}}@media print{&:before{outline-offset:-1rem;outline:.25rem solid}}&:focus-visible,&:has(:focus-visible){outline-offset:2px;outline:2px solid}&:checked,&[aria-checked=true],&:has(>input:checked){background-color:var(--color-base-100);--input-color:var(--color-base-content);grid-template-columns:1fr 1fr 0fr;&:before{background-color:currentColor}@starting-style{&:before{opacity:0}}}&:indeterminate{grid-template-columns:.5fr 1fr .5fr}&:disabled{cursor:not-allowed;opacity:.3;&:before{border:var(--border)solid currentColor;background-color:#0000}}}.toggle-primary{&:checked,&[aria-checked=true]{--input-color:var(--color-primary)}}.toggle-secondary{&:checked,&[aria-checked=true]{--input-color:var(--color-secondary)}}.toggle-accent{&:checked,&[aria-checked=true]{--input-color:var(--color-accent)}}.toggle-neutral{&:checked,&[aria-checked=true]{--input-color:var(--color-neutral)}}.toggle-success{&:checked,&[aria-checked=true]{--input-color:var(--color-success)}}.toggle-warning{&:checked,&[aria-checked=true]{--input-color:var(--color-warning)}}.toggle-info{&:checked,&[aria-checked=true]{--input-color:var(--color-info)}}.toggle-error{&:checked,&[aria-checked=true]{--input-color:var(--color-error)}}.toggle-xs{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*4)}}.toggle-sm{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*5)}}.toggle-md{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*6)}}.toggle-lg{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*7)}}.toggle-xl{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*8)}}@media (width>=640px){.sm\:toggle{border:var(--border)solid currentColor;color:var(--input-color);cursor:pointer;appearance:none;vertical-align:middle;-webkit-user-select:none;user-select:none;--radius-selector-max:calc(var(--radius-selector) + var(--radius-selector) + var(--radius-selector));border-radius:calc(var(--radius-selector) + min(var(--toggle-p),var(--radius-selector-max)) + min(var(--border),var(--radius-selector-max)));padding:var(--toggle-p);box-shadow:0 1px color-mix(in oklab,currentColor calc(var(--depth)*10%),#0000)inset;--input-color:color-mix(in oklab,var(--color-base-content)50%,#0000);--toggle-p:calc(var(--size)*.125);--size:calc(var(--size-selector,.25rem)*6);width:calc((var(--size)*2) - (var(--border) + var(--toggle-p))*2);height:var(--size);flex-shrink:0;grid-template-columns:0fr 1fr 1fr;place-content:center;transition:color .3s,grid-template-columns .2s;display:inline-grid;position:relative;&>*{z-index:1;cursor:pointer;appearance:none;background-color:#0000;border:none;grid-column:2/span 1;grid-row-start:1;height:100%;padding:.125rem;transition:opacity .2s,rotate .4s;&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:nth-child(2){color:var(--color-base-100);rotate:none}&:nth-child(3){color:var(--color-base-100);opacity:0;rotate:-15deg}}&:has(:checked){&>:nth-child(2){opacity:0;rotate:15deg}&>:nth-child(3){opacity:1;rotate:none}}&:before{aspect-ratio:1;border-radius:var(--radius-selector);--tw-content:"";content:var(--tw-content);height:100%;box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1))inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px color-mix(in oklab,currentColor calc(var(--depth)*10%),#0000);background-color:currentColor;background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);grid-row-start:1;grid-column-start:2;transition:background-color .1s,translate .2s,inset-inline-start .2s;position:relative;inset-inline-start:0;translate:0}@media (forced-colors:active){&:before{outline-style:var(--tw-outline-style);outline-offset:calc(1px*-1);outline-width:1px}}@media print{&:before{outline-offset:-1rem;outline:.25rem solid}}&:focus-visible,&:has(:focus-visible){outline-offset:2px;outline:2px solid}&:checked,&[aria-checked=true],&:has(>input:checked){background-color:var(--color-base-100);--input-color:var(--color-base-content);grid-template-columns:1fr 1fr 0fr;&:before{background-color:currentColor}@starting-style{&:before{opacity:0}}}&:indeterminate{grid-template-columns:.5fr 1fr .5fr}&:disabled{cursor:not-allowed;opacity:.3;&:before{border:var(--border)solid currentColor;background-color:#0000}}}.sm\:toggle-primary{&:checked,&[aria-checked=true]{--input-color:var(--color-primary)}}.sm\:toggle-secondary{&:checked,&[aria-checked=true]{--input-color:var(--color-secondary)}}.sm\:toggle-accent{&:checked,&[aria-checked=true]{--input-color:var(--color-accent)}}.sm\:toggle-neutral{&:checked,&[aria-checked=true]{--input-color:var(--color-neutral)}}.sm\:toggle-success{&:checked,&[aria-checked=true]{--input-color:var(--color-success)}}.sm\:toggle-warning{&:checked,&[aria-checked=true]{--input-color:var(--color-warning)}}.sm\:toggle-info{&:checked,&[aria-checked=true]{--input-color:var(--color-info)}}.sm\:toggle-error{&:checked,&[aria-checked=true]{--input-color:var(--color-error)}}.sm\:toggle-xs{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*4)}}.sm\:toggle-sm{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*5)}}.sm\:toggle-md{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*6)}}.sm\:toggle-lg{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*7)}}.sm\:toggle-xl{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*8)}}}@media (width>=768px){.md\:toggle{border:var(--border)solid currentColor;color:var(--input-color);cursor:pointer;appearance:none;vertical-align:middle;-webkit-user-select:none;user-select:none;--radius-selector-max:calc(var(--radius-selector) + var(--radius-selector) + var(--radius-selector));border-radius:calc(var(--radius-selector) + min(var(--toggle-p),var(--radius-selector-max)) + min(var(--border),var(--radius-selector-max)));padding:var(--toggle-p);box-shadow:0 1px color-mix(in oklab,currentColor calc(var(--depth)*10%),#0000)inset;--input-color:color-mix(in oklab,var(--color-base-content)50%,#0000);--toggle-p:calc(var(--size)*.125);--size:calc(var(--size-selector,.25rem)*6);width:calc((var(--size)*2) - (var(--border) + var(--toggle-p))*2);height:var(--size);flex-shrink:0;grid-template-columns:0fr 1fr 1fr;place-content:center;transition:color .3s,grid-template-columns .2s;display:inline-grid;position:relative;&>*{z-index:1;cursor:pointer;appearance:none;background-color:#0000;border:none;grid-column:2/span 1;grid-row-start:1;height:100%;padding:.125rem;transition:opacity .2s,rotate .4s;&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:nth-child(2){color:var(--color-base-100);rotate:none}&:nth-child(3){color:var(--color-base-100);opacity:0;rotate:-15deg}}&:has(:checked){&>:nth-child(2){opacity:0;rotate:15deg}&>:nth-child(3){opacity:1;rotate:none}}&:before{aspect-ratio:1;border-radius:var(--radius-selector);--tw-content:"";content:var(--tw-content);height:100%;box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1))inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px color-mix(in oklab,currentColor calc(var(--depth)*10%),#0000);background-color:currentColor;background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);grid-row-start:1;grid-column-start:2;transition:background-color .1s,translate .2s,inset-inline-start .2s;position:relative;inset-inline-start:0;translate:0}@media (forced-colors:active){&:before{outline-style:var(--tw-outline-style);outline-offset:calc(1px*-1);outline-width:1px}}@media print{&:before{outline-offset:-1rem;outline:.25rem solid}}&:focus-visible,&:has(:focus-visible){outline-offset:2px;outline:2px solid}&:checked,&[aria-checked=true],&:has(>input:checked){background-color:var(--color-base-100);--input-color:var(--color-base-content);grid-template-columns:1fr 1fr 0fr;&:before{background-color:currentColor}@starting-style{&:before{opacity:0}}}&:indeterminate{grid-template-columns:.5fr 1fr .5fr}&:disabled{cursor:not-allowed;opacity:.3;&:before{border:var(--border)solid currentColor;background-color:#0000}}}.md\:toggle-primary{&:checked,&[aria-checked=true]{--input-color:var(--color-primary)}}.md\:toggle-secondary{&:checked,&[aria-checked=true]{--input-color:var(--color-secondary)}}.md\:toggle-accent{&:checked,&[aria-checked=true]{--input-color:var(--color-accent)}}.md\:toggle-neutral{&:checked,&[aria-checked=true]{--input-color:var(--color-neutral)}}.md\:toggle-success{&:checked,&[aria-checked=true]{--input-color:var(--color-success)}}.md\:toggle-warning{&:checked,&[aria-checked=true]{--input-color:var(--color-warning)}}.md\:toggle-info{&:checked,&[aria-checked=true]{--input-color:var(--color-info)}}.md\:toggle-error{&:checked,&[aria-checked=true]{--input-color:var(--color-error)}}.md\:toggle-xs{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*4)}}.md\:toggle-sm{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*5)}}.md\:toggle-md{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*6)}}.md\:toggle-lg{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*7)}}.md\:toggle-xl{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*8)}}}@media (width>=1024px){.lg\:toggle{border:var(--border)solid currentColor;color:var(--input-color);cursor:pointer;appearance:none;vertical-align:middle;-webkit-user-select:none;user-select:none;--radius-selector-max:calc(var(--radius-selector) + var(--radius-selector) + var(--radius-selector));border-radius:calc(var(--radius-selector) + min(var(--toggle-p),var(--radius-selector-max)) + min(var(--border),var(--radius-selector-max)));padding:var(--toggle-p);box-shadow:0 1px color-mix(in oklab,currentColor calc(var(--depth)*10%),#0000)inset;--input-color:color-mix(in oklab,var(--color-base-content)50%,#0000);--toggle-p:calc(var(--size)*.125);--size:calc(var(--size-selector,.25rem)*6);width:calc((var(--size)*2) - (var(--border) + var(--toggle-p))*2);height:var(--size);flex-shrink:0;grid-template-columns:0fr 1fr 1fr;place-content:center;transition:color .3s,grid-template-columns .2s;display:inline-grid;position:relative;&>*{z-index:1;cursor:pointer;appearance:none;background-color:#0000;border:none;grid-column:2/span 1;grid-row-start:1;height:100%;padding:.125rem;transition:opacity .2s,rotate .4s;&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:nth-child(2){color:var(--color-base-100);rotate:none}&:nth-child(3){color:var(--color-base-100);opacity:0;rotate:-15deg}}&:has(:checked){&>:nth-child(2){opacity:0;rotate:15deg}&>:nth-child(3){opacity:1;rotate:none}}&:before{aspect-ratio:1;border-radius:var(--radius-selector);--tw-content:"";content:var(--tw-content);height:100%;box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1))inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px color-mix(in oklab,currentColor calc(var(--depth)*10%),#0000);background-color:currentColor;background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);grid-row-start:1;grid-column-start:2;transition:background-color .1s,translate .2s,inset-inline-start .2s;position:relative;inset-inline-start:0;translate:0}@media (forced-colors:active){&:before{outline-style:var(--tw-outline-style);outline-offset:calc(1px*-1);outline-width:1px}}@media print{&:before{outline-offset:-1rem;outline:.25rem solid}}&:focus-visible,&:has(:focus-visible){outline-offset:2px;outline:2px solid}&:checked,&[aria-checked=true],&:has(>input:checked){background-color:var(--color-base-100);--input-color:var(--color-base-content);grid-template-columns:1fr 1fr 0fr;&:before{background-color:currentColor}@starting-style{&:before{opacity:0}}}&:indeterminate{grid-template-columns:.5fr 1fr .5fr}&:disabled{cursor:not-allowed;opacity:.3;&:before{border:var(--border)solid currentColor;background-color:#0000}}}.lg\:toggle-primary{&:checked,&[aria-checked=true]{--input-color:var(--color-primary)}}.lg\:toggle-secondary{&:checked,&[aria-checked=true]{--input-color:var(--color-secondary)}}.lg\:toggle-accent{&:checked,&[aria-checked=true]{--input-color:var(--color-accent)}}.lg\:toggle-neutral{&:checked,&[aria-checked=true]{--input-color:var(--color-neutral)}}.lg\:toggle-success{&:checked,&[aria-checked=true]{--input-color:var(--color-success)}}.lg\:toggle-warning{&:checked,&[aria-checked=true]{--input-color:var(--color-warning)}}.lg\:toggle-info{&:checked,&[aria-checked=true]{--input-color:var(--color-info)}}.lg\:toggle-error{&:checked,&[aria-checked=true]{--input-color:var(--color-error)}}.lg\:toggle-xs{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*4)}}.lg\:toggle-sm{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*5)}}.lg\:toggle-md{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*6)}}.lg\:toggle-lg{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*7)}}.lg\:toggle-xl{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*8)}}}@media (width>=1280px){.xl\:toggle{border:var(--border)solid currentColor;color:var(--input-color);cursor:pointer;appearance:none;vertical-align:middle;-webkit-user-select:none;user-select:none;--radius-selector-max:calc(var(--radius-selector) + var(--radius-selector) + var(--radius-selector));border-radius:calc(var(--radius-selector) + min(var(--toggle-p),var(--radius-selector-max)) + min(var(--border),var(--radius-selector-max)));padding:var(--toggle-p);box-shadow:0 1px color-mix(in oklab,currentColor calc(var(--depth)*10%),#0000)inset;--input-color:color-mix(in oklab,var(--color-base-content)50%,#0000);--toggle-p:calc(var(--size)*.125);--size:calc(var(--size-selector,.25rem)*6);width:calc((var(--size)*2) - (var(--border) + var(--toggle-p))*2);height:var(--size);flex-shrink:0;grid-template-columns:0fr 1fr 1fr;place-content:center;transition:color .3s,grid-template-columns .2s;display:inline-grid;position:relative;&>*{z-index:1;cursor:pointer;appearance:none;background-color:#0000;border:none;grid-column:2/span 1;grid-row-start:1;height:100%;padding:.125rem;transition:opacity .2s,rotate .4s;&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:nth-child(2){color:var(--color-base-100);rotate:none}&:nth-child(3){color:var(--color-base-100);opacity:0;rotate:-15deg}}&:has(:checked){&>:nth-child(2){opacity:0;rotate:15deg}&>:nth-child(3){opacity:1;rotate:none}}&:before{aspect-ratio:1;border-radius:var(--radius-selector);--tw-content:"";content:var(--tw-content);height:100%;box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1))inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px color-mix(in oklab,currentColor calc(var(--depth)*10%),#0000);background-color:currentColor;background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);grid-row-start:1;grid-column-start:2;transition:background-color .1s,translate .2s,inset-inline-start .2s;position:relative;inset-inline-start:0;translate:0}@media (forced-colors:active){&:before{outline-style:var(--tw-outline-style);outline-offset:calc(1px*-1);outline-width:1px}}@media print{&:before{outline-offset:-1rem;outline:.25rem solid}}&:focus-visible,&:has(:focus-visible){outline-offset:2px;outline:2px solid}&:checked,&[aria-checked=true],&:has(>input:checked){background-color:var(--color-base-100);--input-color:var(--color-base-content);grid-template-columns:1fr 1fr 0fr;&:before{background-color:currentColor}@starting-style{&:before{opacity:0}}}&:indeterminate{grid-template-columns:.5fr 1fr .5fr}&:disabled{cursor:not-allowed;opacity:.3;&:before{border:var(--border)solid currentColor;background-color:#0000}}}.xl\:toggle-primary{&:checked,&[aria-checked=true]{--input-color:var(--color-primary)}}.xl\:toggle-secondary{&:checked,&[aria-checked=true]{--input-color:var(--color-secondary)}}.xl\:toggle-accent{&:checked,&[aria-checked=true]{--input-color:var(--color-accent)}}.xl\:toggle-neutral{&:checked,&[aria-checked=true]{--input-color:var(--color-neutral)}}.xl\:toggle-success{&:checked,&[aria-checked=true]{--input-color:var(--color-success)}}.xl\:toggle-warning{&:checked,&[aria-checked=true]{--input-color:var(--color-warning)}}.xl\:toggle-info{&:checked,&[aria-checked=true]{--input-color:var(--color-info)}}.xl\:toggle-error{&:checked,&[aria-checked=true]{--input-color:var(--color-error)}}.xl\:toggle-xs{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*4)}}.xl\:toggle-sm{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*5)}}.xl\:toggle-md{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*6)}}.xl\:toggle-lg{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*7)}}.xl\:toggle-xl{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*8)}}}@media (width>=1536px){.\32 xl\:toggle{border:var(--border)solid currentColor;color:var(--input-color);cursor:pointer;appearance:none;vertical-align:middle;-webkit-user-select:none;user-select:none;--radius-selector-max:calc(var(--radius-selector) + var(--radius-selector) + var(--radius-selector));border-radius:calc(var(--radius-selector) + min(var(--toggle-p),var(--radius-selector-max)) + min(var(--border),var(--radius-selector-max)));padding:var(--toggle-p);box-shadow:0 1px color-mix(in oklab,currentColor calc(var(--depth)*10%),#0000)inset;--input-color:color-mix(in oklab,var(--color-base-content)50%,#0000);--toggle-p:calc(var(--size)*.125);--size:calc(var(--size-selector,.25rem)*6);width:calc((var(--size)*2) - (var(--border) + var(--toggle-p))*2);height:var(--size);flex-shrink:0;grid-template-columns:0fr 1fr 1fr;place-content:center;transition:color .3s,grid-template-columns .2s;display:inline-grid;position:relative;&>*{z-index:1;cursor:pointer;appearance:none;background-color:#0000;border:none;grid-column:2/span 1;grid-row-start:1;height:100%;padding:.125rem;transition:opacity .2s,rotate .4s;&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:nth-child(2){color:var(--color-base-100);rotate:none}&:nth-child(3){color:var(--color-base-100);opacity:0;rotate:-15deg}}&:has(:checked){&>:nth-child(2){opacity:0;rotate:15deg}&>:nth-child(3){opacity:1;rotate:none}}&:before{aspect-ratio:1;border-radius:var(--radius-selector);--tw-content:"";content:var(--tw-content);height:100%;box-shadow:0 -1px oklch(0% 0 0/calc(var(--depth)*.1))inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px color-mix(in oklab,currentColor calc(var(--depth)*10%),#0000);background-color:currentColor;background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);grid-row-start:1;grid-column-start:2;transition:background-color .1s,translate .2s,inset-inline-start .2s;position:relative;inset-inline-start:0;translate:0}@media (forced-colors:active){&:before{outline-style:var(--tw-outline-style);outline-offset:calc(1px*-1);outline-width:1px}}@media print{&:before{outline-offset:-1rem;outline:.25rem solid}}&:focus-visible,&:has(:focus-visible){outline-offset:2px;outline:2px solid}&:checked,&[aria-checked=true],&:has(>input:checked){background-color:var(--color-base-100);--input-color:var(--color-base-content);grid-template-columns:1fr 1fr 0fr;&:before{background-color:currentColor}@starting-style{&:before{opacity:0}}}&:indeterminate{grid-template-columns:.5fr 1fr .5fr}&:disabled{cursor:not-allowed;opacity:.3;&:before{border:var(--border)solid currentColor;background-color:#0000}}}.\32 xl\:toggle-primary{&:checked,&[aria-checked=true]{--input-color:var(--color-primary)}}.\32 xl\:toggle-secondary{&:checked,&[aria-checked=true]{--input-color:var(--color-secondary)}}.\32 xl\:toggle-accent{&:checked,&[aria-checked=true]{--input-color:var(--color-accent)}}.\32 xl\:toggle-neutral{&:checked,&[aria-checked=true]{--input-color:var(--color-neutral)}}.\32 xl\:toggle-success{&:checked,&[aria-checked=true]{--input-color:var(--color-success)}}.\32 xl\:toggle-warning{&:checked,&[aria-checked=true]{--input-color:var(--color-warning)}}.\32 xl\:toggle-info{&:checked,&[aria-checked=true]{--input-color:var(--color-info)}}.\32 xl\:toggle-error{&:checked,&[aria-checked=true]{--input-color:var(--color-error)}}.\32 xl\:toggle-xs{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*4)}}.\32 xl\:toggle-sm{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*5)}}.\32 xl\:toggle-md{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*6)}}.\32 xl\:toggle-lg{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*7)}}.\32 xl\:toggle-xl{&[type=checkbox],&:has([type=checkbox]){--size:calc(var(--size-selector,.25rem)*8)}}}}