#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create sample data for TaskSphere backend
Run this script after setting up the database to populate it with test data
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tasksphere_backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from core.models import Company, CompanyMembership, Group, GroupMembership, Task
from notifications.views import create_notification
from datetime import datetime, timedelta
from django.utils import timezone

User = get_user_model()

def create_sample_data():
    print("Creating sample data for TaskSphere...")
    
    # Create companies
    company1 = Company.objects.create(
        name="TechCorp Solutions",
        description="A leading technology solutions company"
    )
    
    company2 = Company.objects.create(
        name="Digital Innovations Inc",
        description="Digital transformation specialists"
    )
    
    print(f"Created companies: {company1.name} ({company1.company_code}), {company2.name} ({company2.company_code})")
    
    # Create users
    admin_user = User.objects.create_user(
        email="<EMAIL>",
        username="admin_techcorp",
        password="admin123",
        first_name="<PERSON>",
        last_name="Admin"
    )
    
    user1 = User.objects.create_user(
        email="<EMAIL>",
        username="alice_dev",
        password="user123",
        first_name="Alice",
        last_name="Developer"
    )
    
    user2 = User.objects.create_user(
        email="<EMAIL>",
        username="bob_designer",
        password="user123",
        first_name="Bob",
        last_name="Designer"
    )
    
    user3 = User.objects.create_user(
        email="<EMAIL>",
        username="carol_tester",
        password="user123",
        first_name="Carol",
        last_name="Tester"
    )
    
    print(f"Created users: {admin_user.user_id}, {user1.user_id}, {user2.user_id}, {user3.user_id}")
    
    # Create company memberships
    CompanyMembership.objects.create(user=admin_user, company=company1, role='admin')
    CompanyMembership.objects.create(user=user1, company=company1, role='user')
    CompanyMembership.objects.create(user=user2, company=company1, role='user')
    CompanyMembership.objects.create(user=user3, company=company1, role='user')
    
    print("Created company memberships")
    
    # Create groups
    dev_group = Group.objects.create(
        name="Development Team",
        description="Software development team",
        company=company1,
        created_by=admin_user
    )
    
    design_group = Group.objects.create(
        name="Design Team",
        description="UI/UX design team",
        company=company1,
        created_by=admin_user
    )
    
    qa_group = Group.objects.create(
        name="QA Team",
        description="Quality assurance team",
        company=company1,
        created_by=admin_user
    )
    
    print("Created groups")
    
    # Add users to groups
    GroupMembership.objects.create(user=user1, group=dev_group, added_by=admin_user)
    GroupMembership.objects.create(user=user2, group=design_group, added_by=admin_user)
    GroupMembership.objects.create(user=user3, group=qa_group, added_by=admin_user)
    GroupMembership.objects.create(user=user1, group=qa_group, added_by=admin_user)  # Alice is in both dev and QA
    
    print("Added users to groups")
    
    # Create tasks
    task1 = Task.objects.create(
        title="Implement User Authentication",
        description="Develop JWT-based authentication system for the application",
        company=company1,
        created_by=admin_user,
        assigned_to=user1,
        assigned_group=dev_group,
        status="in_progress",
        priority="high",
        deadline=timezone.now() + timedelta(days=7)
    )
    
    task2 = Task.objects.create(
        title="Design Login Page",
        description="Create wireframes and mockups for the login page",
        company=company1,
        created_by=admin_user,
        assigned_to=user2,
        assigned_group=design_group,
        status="pending",
        priority="medium",
        deadline=timezone.now() + timedelta(days=5)
    )
    
    task3 = Task.objects.create(
        title="Test Authentication Flow",
        description="Write and execute test cases for user authentication",
        company=company1,
        created_by=admin_user,
        assigned_to=user3,
        assigned_group=qa_group,
        status="pending",
        priority="medium",
        deadline=timezone.now() + timedelta(days=10)
    )
    
    task4 = Task.objects.create(
        title="Database Optimization",
        description="Optimize database queries for better performance",
        company=company1,
        created_by=admin_user,
        assigned_to=user1,
        status="completed",
        priority="low",
        deadline=timezone.now() - timedelta(days=2),
        completed_at=timezone.now() - timedelta(days=1)
    )
    
    print("Created tasks")
    
    # Create notifications
    create_notification(
        recipient=user1,
        title="New Task Assigned",
        message=f"You have been assigned a new task: {task1.title}",
        notification_type="task_assigned",
        related_task=task1
    )
    
    create_notification(
        recipient=user2,
        title="New Task Assigned",
        message=f"You have been assigned a new task: {task2.title}",
        notification_type="task_assigned",
        related_task=task2
    )
    
    create_notification(
        recipient=user3,
        title="Task Deadline Approaching",
        message=f"Task '{task3.title}' deadline is approaching",
        notification_type="task_deadline",
        related_task=task3
    )
    
    print("Created notifications")
    
    print("\n" + "="*50)
    print("SAMPLE DATA CREATED SUCCESSFULLY!")
    print("="*50)
    print(f"Company: {company1.name}")
    print(f"Company Code: {company1.company_code}")
    print("\nUsers created:")
    print(f"Admin: {admin_user.email} (ID: {admin_user.user_id}) - Password: admin123")
    print(f"Alice: {user1.email} (ID: {user1.user_id}) - Password: user123")
    print(f"Bob: {user2.email} (ID: {user2.user_id}) - Password: user123")
    print(f"Carol: {user3.email} (ID: {user3.user_id}) - Password: user123")
    print("\nYou can now test the API endpoints with these credentials!")
    print("="*50)

if __name__ == "__main__":
    create_sample_data()
