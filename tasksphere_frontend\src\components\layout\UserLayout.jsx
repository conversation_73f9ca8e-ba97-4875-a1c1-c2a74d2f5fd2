import { useState } from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import {
  Home,
  CheckSquare,
  ArrowRightLeft,
  Bell,
  User,
  LogOut,
  Menu,
  Calendar,
  MessageSquare,
} from 'lucide-react';

const UserLayout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
    navigate('/user/login');
  };

  const navigation = [
    { name: 'Dashboard', href: '/user/dashboard', icon: Home },
    { name: 'My Tasks', href: '/user/tasks', icon: CheckSquare },
    { name: 'Task Swaps', href: '/user/swaps', icon: ArrowRightLeft },
    { name: 'Calendar', href: '/user/calendar', icon: Calendar },
    { name: 'Messages', href: '/user/messages', icon: MessageSquare },
    { name: 'Notifications', href: '/user/notifications', icon: Bell },
  ];

  const isActive = (href) => location.pathname === href;

  return (
    <div className="drawer lg:drawer-open">
      <input id="user-drawer-toggle" type="checkbox" className="drawer-toggle" checked={sidebarOpen} onChange={() => setSidebarOpen(!sidebarOpen)} />
      
      {/* Main content */}
      <div className="drawer-content flex flex-col">
        {/* Top bar */}
        <div className="navbar bg-green-600 text-white shadow-lg lg:hidden">
          <div className="flex-none">
            <label htmlFor="user-drawer-toggle" className="btn btn-square btn-ghost text-white">
              <Menu className="h-5 w-5" />
            </label>
          </div>
          <div className="flex-1">
            <h1 className="text-xl font-bold">TaskSphere</h1>
          </div>
          <div className="flex-none">
            <Link to="/user/notifications" className="btn btn-ghost btn-sm text-white">
              <Bell className="h-5 w-5" />
            </Link>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1 p-6 bg-green-50 min-h-screen lg:pt-20 lg:pl-64">
          <Outlet />
        </main>
      </div>

      {/* Sidebar */}
      <div className="drawer-side">
        <label htmlFor="user-drawer-toggle" className="drawer-overlay"></label>
        <aside className="w-64 min-h-full bg-green-800 text-white shadow-lg">
          {/* Sidebar Header */}
          <div className="flex items-center justify-center h-16 px-4 border-b border-green-700 bg-green-900">
            <User className="h-6 w-6 mr-2" />
            <h1 className="text-xl font-bold">User Portal</h1>
          </div>

          {/* Navigation */}
          <nav className="mt-8">
            <div className="px-4 space-y-2">
              {navigation.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                      isActive(item.href)
                        ? 'bg-green-600 text-white'
                        : 'text-green-100 hover:bg-green-700 hover:text-white'
                    }`}
                    onClick={() => setSidebarOpen(false)}
                  >
                    <Icon className="h-5 w-5 mr-3" />
                    {item.name}
                  </Link>
                );
              })}
            </div>
          </nav>

          {/* User info at bottom */}
          <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-green-700">
            <div className="flex items-center space-x-3 mb-3">
              <div className="avatar placeholder">
                <div className="bg-green-600 text-white rounded-full w-10">
                  <span className="text-sm">
                    {user?.first_name?.[0]}{user?.last_name?.[0]}
                  </span>
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  {user?.first_name} {user?.last_name}
                </p>
                <p className="text-xs text-green-200 truncate">
                  User • ID: {user?.user_id}
                </p>
              </div>
            </div>
            
            <div className="flex space-x-2">
              <Link
                to="/user/profile"
                className="btn btn-ghost btn-sm flex-1 text-green-100 hover:text-white"
                onClick={() => setSidebarOpen(false)}
              >
                <User className="h-4 w-4 mr-1" />
                Profile
              </Link>
              <button
                onClick={handleLogout}
                className="btn btn-ghost btn-sm flex-1 text-green-100 hover:text-white"
              >
                <LogOut className="h-4 w-4 mr-1" />
                Logout
              </button>
            </div>
          </div>
        </aside>
      </div>

      {/* Desktop top bar */}
      <div className="hidden lg:block fixed top-0 left-64 right-0 z-10 bg-white shadow-sm border-b border-gray-200">
        <div className="flex items-center justify-between h-16 px-6">
          <div className="flex-1">
            <h2 className="text-lg font-semibold text-gray-800">My Workspace</h2>
          </div>
          <div className="flex items-center space-x-4">
            {/* Company info */}
            {user?.companies && user.companies.length > 0 && (
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <span>Company:</span>
                <span className="font-medium">{user.companies[0].company_name}</span>
              </div>
            )}

            {/* Notifications */}
            <Link to="/user/notifications" className="btn btn-ghost btn-sm">
              <Bell className="h-5 w-5" />
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserLayout;
