import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { notificationsAPI } from '../../services/api';
import {
  Bell,
  CheckCircle,
  AlertCircle,
  ArrowRightLeft,
  CheckSquare,
  Clock,
} from 'lucide-react';
import toast from 'react-hot-toast';

const NotificationList = () => {
  const queryClient = useQueryClient();

  const { data: notifications = [], isLoading, error } = useQuery({
    queryKey: ['notifications'],
    queryFn: notificationsAPI.getNotifications,
  });

  const markAsReadMutation = useMutation({
    mutationFn: notificationsAPI.markNotificationRead,
    onSuccess: () => {
      queryClient.invalidateQueries(['notifications']);
      toast.success('Notification marked as read');
    },
  });

  const markAllAsReadMutation = useMutation({
    mutationFn: notificationsAPI.markAllNotificationsRead,
    onSuccess: () => {
      queryClient.invalidateQueries(['notifications']);
      toast.success('All notifications marked as read');
    },
  });

  const getNotificationIcon = (type) => {
    const iconMap = {
      task_assigned: CheckSquare,
      task_deadline: Clock,
      swap_request: ArrowRightLeft,
      swap_admin_approval: AlertCircle,
      swap_approved: CheckCircle,
      swap_rejected: AlertCircle,
      task_overdue: AlertCircle,
      task_completed: CheckCircle,
    };
    return iconMap[type] || Bell;
  };

  const getNotificationColor = (type) => {
    const colorMap = {
      task_assigned: 'text-primary',
      task_deadline: 'text-warning',
      swap_request: 'text-info',
      swap_admin_approval: 'text-warning',
      swap_approved: 'text-success',
      swap_rejected: 'text-error',
      task_overdue: 'text-error',
      task_completed: 'text-success',
    };
    return colorMap[type] || 'text-base-content';
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} hours ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const unreadCount = notifications.filter(n => !n.is_read).length;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-error">
        <AlertCircle className="h-5 w-5" />
        <span>Failed to load notifications. Please try again.</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Notifications</h1>
          <p className="text-base-content/70 mt-1">
            Stay updated with your tasks and activities
          </p>
        </div>
        {unreadCount > 0 && (
          <button
            className="btn btn-outline btn-sm"
            onClick={() => markAllAsReadMutation.mutate()}
            disabled={markAllAsReadMutation.isLoading}
          >
            {markAllAsReadMutation.isLoading ? (
              <span className="loading loading-spinner loading-sm"></span>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Mark All Read
              </>
            )}
          </button>
        )}
      </div>

      {/* Stats */}
      <div className="stats shadow">
        <div className="stat">
          <div className="stat-title">Total Notifications</div>
          <div className="stat-value text-primary">{notifications.length}</div>
        </div>
        <div className="stat">
          <div className="stat-title">Unread</div>
          <div className="stat-value text-warning">{unreadCount}</div>
        </div>
        <div className="stat">
          <div className="stat-title">Read</div>
          <div className="stat-value text-success">{notifications.length - unreadCount}</div>
        </div>
      </div>

      {/* Notifications List */}
      {notifications.length === 0 ? (
        <div className="text-center py-12">
          <Bell className="h-16 w-16 mx-auto mb-4 text-base-content/30" />
          <h3 className="text-lg font-medium mb-2">No notifications</h3>
          <p className="text-base-content/70">
            You're all caught up! New notifications will appear here.
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {notifications.map((notification) => {
            const Icon = getNotificationIcon(notification.notification_type);
            const iconColor = getNotificationColor(notification.notification_type);

            return (
              <div
                key={notification.id}
                className={`card bg-base-100 shadow-sm hover:shadow-md transition-shadow ${
                  !notification.is_read ? 'border-l-4 border-l-primary' : ''
                }`}
              >
                <div className="card-body p-4">
                  <div className="flex items-start gap-4">
                    <div className={`p-2 rounded-lg bg-base-200 ${iconColor}`}>
                      <Icon className="h-5 w-5" />
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <h3 className={`font-medium ${!notification.is_read ? 'font-bold' : ''}`}>
                          {notification.title}
                        </h3>
                        <div className="flex items-center gap-2 ml-4">
                          <span className="text-xs text-base-content/70">
                            {formatDate(notification.created_at)}
                          </span>
                          {!notification.is_read && (
                            <button
                              className="btn btn-ghost btn-xs"
                              onClick={() => markAsReadMutation.mutate(notification.id)}
                              disabled={markAsReadMutation.isLoading}
                            >
                              <CheckCircle className="h-3 w-3" />
                            </button>
                          )}
                        </div>
                      </div>

                      <p className="text-sm text-base-content/70 mt-1">
                        {notification.message}
                      </p>

                      <div className="flex items-center gap-2 mt-2">
                        <span className="badge badge-sm badge-outline">
                          {notification.notification_type.replace('_', ' ')}
                        </span>
                        {!notification.is_read && (
                          <span className="badge badge-primary badge-sm">New</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default NotificationList;
