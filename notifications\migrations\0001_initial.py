# Generated by Django 5.2 on 2025-07-10 18:13

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(max_length=200)),
                ('message', models.TextField()),
                ('notification_type', models.CharField(choices=[('task_assigned', 'Task Assigned'), ('task_deadline', 'Task Deadline Approaching'), ('swap_request', 'Swap Request Received'), ('swap_admin_approval', 'Swap Request Pending Admin Approval'), ('swap_approved', 'Swap Request Approved'), ('swap_rejected', 'Swap Request Rejected'), ('task_overdue', 'Task Overdue'), ('task_completed', 'Task Completed')], max_length=20)),
                ('is_read', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
                ('related_swap', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='core.taskswap')),
                ('related_task', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='core.task')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
