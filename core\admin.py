from django.contrib import admin
from .models import Company, CompanyMembership, Group, GroupMembership, Task, TaskSwap


@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ['name', 'company_code', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'company_code']
    readonly_fields = ['company_code', 'created_at', 'updated_at']


@admin.register(CompanyMembership)
class CompanyMembershipAdmin(admin.ModelAdmin):
    list_display = ['user', 'company', 'role', 'is_active', 'joined_at']
    list_filter = ['role', 'is_active', 'joined_at']
    search_fields = ['user__user_id', 'user__email', 'company__name']


@admin.register(Group)
class GroupAdmin(admin.ModelAdmin):
    list_display = ['name', 'company', 'created_by', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at', 'company']
    search_fields = ['name', 'company__name']


@admin.register(GroupMembership)
class GroupMembershipAdmin(admin.ModelAdmin):
    list_display = ['user', 'group', 'added_by', 'is_active', 'joined_at']
    list_filter = ['is_active', 'joined_at']
    search_fields = ['user__user_id', 'group__name']


@admin.register(Task)
class TaskAdmin(admin.ModelAdmin):
    list_display = ['title', 'company', 'assigned_to', 'status', 'priority', 'deadline', 'created_at']
    list_filter = ['status', 'priority', 'created_at', 'company']
    search_fields = ['title', 'assigned_to__user_id', 'company__name']
    date_hierarchy = 'created_at'


@admin.register(TaskSwap)
class TaskSwapAdmin(admin.ModelAdmin):
    list_display = ['task', 'requested_by', 'requested_to', 'status', 'created_at']
    list_filter = ['status', 'created_at']
    search_fields = ['task__title', 'requested_by__user_id', 'requested_to__user_id']
