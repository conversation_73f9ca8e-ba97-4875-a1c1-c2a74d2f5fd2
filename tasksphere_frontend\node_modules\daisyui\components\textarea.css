/*! 🌼 daisyUI 5.0.45 - MIT License */ @layer utilities{.textarea{border:var(--border)solid #0000;appearance:none;border-radius:var(--radius-field);background-color:var(--color-base-100);vertical-align:middle;touch-action:manipulation;border-color:var(--input-color);width:clamp(3rem,20rem,100%);min-height:5rem;box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);flex-shrink:1;padding-block:.5rem;padding-inline:.75rem;font-size:.875rem;& textarea{appearance:none;background-color:#0000;border:none;&:focus,&:focus-within{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}}&:focus,&:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate}&:has(>textarea[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content)40%,transparent);box-shadow:none;&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}&:has(>textarea[disabled])>textarea[disabled]{cursor:not-allowed}}.textarea-ghost{box-shadow:none;background-color:#0000;border-color:#0000;&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.textarea-neutral{&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.textarea-primary{&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.textarea-secondary{&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.textarea-accent{&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.textarea-info{&,&:focus,&:focus-within{--input-color:var(--color-info)}}.textarea-success{&,&:focus,&:focus-within{--input-color:var(--color-success)}}.textarea-warning{&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.textarea-error{&,&:focus,&:focus-within{--input-color:var(--color-error)}}.textarea-xs{font-size:.6875rem}.textarea-sm{font-size:.75rem}.textarea-md{font-size:.875rem}.textarea-lg{font-size:1.125rem}.textarea-xl{font-size:1.375rem}@media (width>=640px){.sm\:textarea{border:var(--border)solid #0000;appearance:none;border-radius:var(--radius-field);background-color:var(--color-base-100);vertical-align:middle;touch-action:manipulation;border-color:var(--input-color);width:clamp(3rem,20rem,100%);min-height:5rem;box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);flex-shrink:1;padding-block:.5rem;padding-inline:.75rem;font-size:.875rem;& textarea{appearance:none;background-color:#0000;border:none;&:focus,&:focus-within{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}}&:focus,&:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate}&:has(>textarea[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content)40%,transparent);box-shadow:none;&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}&:has(>textarea[disabled])>textarea[disabled]{cursor:not-allowed}}.sm\:textarea-ghost{box-shadow:none;background-color:#0000;border-color:#0000;&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.sm\:textarea-neutral{&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.sm\:textarea-primary{&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.sm\:textarea-secondary{&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.sm\:textarea-accent{&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.sm\:textarea-info{&,&:focus,&:focus-within{--input-color:var(--color-info)}}.sm\:textarea-success{&,&:focus,&:focus-within{--input-color:var(--color-success)}}.sm\:textarea-warning{&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.sm\:textarea-error{&,&:focus,&:focus-within{--input-color:var(--color-error)}}.sm\:textarea-xs{font-size:.6875rem}.sm\:textarea-sm{font-size:.75rem}.sm\:textarea-md{font-size:.875rem}.sm\:textarea-lg{font-size:1.125rem}.sm\:textarea-xl{font-size:1.375rem}}@media (width>=768px){.md\:textarea{border:var(--border)solid #0000;appearance:none;border-radius:var(--radius-field);background-color:var(--color-base-100);vertical-align:middle;touch-action:manipulation;border-color:var(--input-color);width:clamp(3rem,20rem,100%);min-height:5rem;box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);flex-shrink:1;padding-block:.5rem;padding-inline:.75rem;font-size:.875rem;& textarea{appearance:none;background-color:#0000;border:none;&:focus,&:focus-within{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}}&:focus,&:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate}&:has(>textarea[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content)40%,transparent);box-shadow:none;&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}&:has(>textarea[disabled])>textarea[disabled]{cursor:not-allowed}}.md\:textarea-ghost{box-shadow:none;background-color:#0000;border-color:#0000;&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.md\:textarea-neutral{&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.md\:textarea-primary{&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.md\:textarea-secondary{&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.md\:textarea-accent{&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.md\:textarea-info{&,&:focus,&:focus-within{--input-color:var(--color-info)}}.md\:textarea-success{&,&:focus,&:focus-within{--input-color:var(--color-success)}}.md\:textarea-warning{&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.md\:textarea-error{&,&:focus,&:focus-within{--input-color:var(--color-error)}}.md\:textarea-xs{font-size:.6875rem}.md\:textarea-sm{font-size:.75rem}.md\:textarea-md{font-size:.875rem}.md\:textarea-lg{font-size:1.125rem}.md\:textarea-xl{font-size:1.375rem}}@media (width>=1024px){.lg\:textarea{border:var(--border)solid #0000;appearance:none;border-radius:var(--radius-field);background-color:var(--color-base-100);vertical-align:middle;touch-action:manipulation;border-color:var(--input-color);width:clamp(3rem,20rem,100%);min-height:5rem;box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);flex-shrink:1;padding-block:.5rem;padding-inline:.75rem;font-size:.875rem;& textarea{appearance:none;background-color:#0000;border:none;&:focus,&:focus-within{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}}&:focus,&:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate}&:has(>textarea[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content)40%,transparent);box-shadow:none;&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}&:has(>textarea[disabled])>textarea[disabled]{cursor:not-allowed}}.lg\:textarea-ghost{box-shadow:none;background-color:#0000;border-color:#0000;&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.lg\:textarea-neutral{&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.lg\:textarea-primary{&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.lg\:textarea-secondary{&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.lg\:textarea-accent{&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.lg\:textarea-info{&,&:focus,&:focus-within{--input-color:var(--color-info)}}.lg\:textarea-success{&,&:focus,&:focus-within{--input-color:var(--color-success)}}.lg\:textarea-warning{&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.lg\:textarea-error{&,&:focus,&:focus-within{--input-color:var(--color-error)}}.lg\:textarea-xs{font-size:.6875rem}.lg\:textarea-sm{font-size:.75rem}.lg\:textarea-md{font-size:.875rem}.lg\:textarea-lg{font-size:1.125rem}.lg\:textarea-xl{font-size:1.375rem}}@media (width>=1280px){.xl\:textarea{border:var(--border)solid #0000;appearance:none;border-radius:var(--radius-field);background-color:var(--color-base-100);vertical-align:middle;touch-action:manipulation;border-color:var(--input-color);width:clamp(3rem,20rem,100%);min-height:5rem;box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);flex-shrink:1;padding-block:.5rem;padding-inline:.75rem;font-size:.875rem;& textarea{appearance:none;background-color:#0000;border:none;&:focus,&:focus-within{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}}&:focus,&:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate}&:has(>textarea[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content)40%,transparent);box-shadow:none;&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}&:has(>textarea[disabled])>textarea[disabled]{cursor:not-allowed}}.xl\:textarea-ghost{box-shadow:none;background-color:#0000;border-color:#0000;&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.xl\:textarea-neutral{&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.xl\:textarea-primary{&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.xl\:textarea-secondary{&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.xl\:textarea-accent{&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.xl\:textarea-info{&,&:focus,&:focus-within{--input-color:var(--color-info)}}.xl\:textarea-success{&,&:focus,&:focus-within{--input-color:var(--color-success)}}.xl\:textarea-warning{&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.xl\:textarea-error{&,&:focus,&:focus-within{--input-color:var(--color-error)}}.xl\:textarea-xs{font-size:.6875rem}.xl\:textarea-sm{font-size:.75rem}.xl\:textarea-md{font-size:.875rem}.xl\:textarea-lg{font-size:1.125rem}.xl\:textarea-xl{font-size:1.375rem}}@media (width>=1536px){.\32 xl\:textarea{border:var(--border)solid #0000;appearance:none;border-radius:var(--radius-field);background-color:var(--color-base-100);vertical-align:middle;touch-action:manipulation;border-color:var(--input-color);width:clamp(3rem,20rem,100%);min-height:5rem;box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000)inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1))inset;--input-color:color-mix(in oklab,var(--color-base-content)20%,#0000);flex-shrink:1;padding-block:.5rem;padding-inline:.75rem;font-size:.875rem;& textarea{appearance:none;background-color:#0000;border:none;&:focus,&:focus-within{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}}&:focus,&:focus-within{--input-color:var(--color-base-content);box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000);outline:2px solid var(--input-color);outline-offset:2px;isolation:isolate}&:has(>textarea[disabled]),&:is(:disabled,[disabled]){cursor:not-allowed;border-color:var(--color-base-200);background-color:var(--color-base-200);color:color-mix(in oklab,var(--color-base-content)40%,transparent);box-shadow:none;&::placeholder{color:color-mix(in oklab,var(--color-base-content)20%,transparent)}}&:has(>textarea[disabled])>textarea[disabled]{cursor:not-allowed}}.\32 xl\:textarea-ghost{box-shadow:none;background-color:#0000;border-color:#0000;&:focus,&:focus-within{background-color:var(--color-base-100);color:var(--color-base-content);box-shadow:none;border-color:#0000}}.\32 xl\:textarea-neutral{&,&:focus,&:focus-within{--input-color:var(--color-neutral)}}.\32 xl\:textarea-primary{&,&:focus,&:focus-within{--input-color:var(--color-primary)}}.\32 xl\:textarea-secondary{&,&:focus,&:focus-within{--input-color:var(--color-secondary)}}.\32 xl\:textarea-accent{&,&:focus,&:focus-within{--input-color:var(--color-accent)}}.\32 xl\:textarea-info{&,&:focus,&:focus-within{--input-color:var(--color-info)}}.\32 xl\:textarea-success{&,&:focus,&:focus-within{--input-color:var(--color-success)}}.\32 xl\:textarea-warning{&,&:focus,&:focus-within{--input-color:var(--color-warning)}}.\32 xl\:textarea-error{&,&:focus,&:focus-within{--input-color:var(--color-error)}}.\32 xl\:textarea-xs{font-size:.6875rem}.\32 xl\:textarea-sm{font-size:.75rem}.\32 xl\:textarea-md{font-size:.875rem}.\32 xl\:textarea-lg{font-size:1.125rem}.\32 xl\:textarea-xl{font-size:1.375rem}}}