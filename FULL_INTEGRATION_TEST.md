# 🧪 TaskSphere Full Integration Test Guide

## 🎯 Complete Backend-Frontend Integration Testing

This guide will help you test every aspect of the TaskSphere platform to ensure the backend and frontend are fully connected and working properly.

## 🚀 Prerequisites

1. **Backend Running**: http://127.0.0.1:8000/
2. **Frontend Running**: http://localhost:5173
3. **Sample Data**: Existing users and company data

## 📋 Test Checklist

### ✅ **Phase 1: Authentication & Basic Setup**

#### 1.1 User Registration
- [ ] Go to http://localhost:5173/register
- [ ] Fill out registration form with:
  - First Name: `Test`
  - Last Name: `User`
  - Username: `testuser`
  - Email: `<EMAIL>`
  - Company Code: `FMZLA1` (from sample data)
  - Password: `testpass123`
- [ ] Verify successful registration and automatic login
- [ ] Check that user is redirected to dashboard

#### 1.2 User Login
- [ ] Go to http://localhost:5173/login
- [ ] Test with demo credentials:
  - Email: `<EMAIL>`
  - Password: `admin123`
- [ ] Verify successful login and redirect to dashboard
- [ ] Check that user info appears in sidebar

#### 1.3 API Connection Test
- [ ] On dashboard, click "Run Tests" in API Connection Test component
- [ ] Verify all 3 tests pass:
  - ✅ Backend Connectivity
  - ✅ Login API
  - ✅ Profile API

### ✅ **Phase 2: Dashboard & Navigation**

#### 2.1 Dashboard Data
- [ ] Verify dashboard shows:
  - Welcome message with user's first name
  - Company badge with name and code
  - Task statistics (Total, In Progress, Overdue, Notifications)
  - Recent tasks list
  - Task swaps list
  - Quick actions buttons

#### 2.2 Navigation
- [ ] Test sidebar navigation:
  - [ ] Dashboard
  - [ ] Tasks
  - [ ] Task Swaps
  - [ ] Groups (admin only)
  - [ ] Notifications
- [ ] Verify active page highlighting
- [ ] Test mobile responsive navigation (resize browser)

### ✅ **Phase 3: Task Management**

#### 3.1 View Tasks
- [ ] Navigate to Tasks page
- [ ] Verify task list displays with:
  - Task cards with title, description, status, priority
  - Assignment information
  - Deadline information
  - Status badges
- [ ] Test filters:
  - [ ] Status filter (All, Pending, In Progress, etc.)
  - [ ] "Assigned to me" checkbox

#### 3.2 Create Task (Admin Only)
- [ ] Login as admin (`<EMAIL>` / `admin123`)
- [ ] Click "New Task" button
- [ ] Fill out task creation form:
  - Title: `Test Integration Task`
  - Description: `Testing the full integration`
  - Priority: `High`
  - Deadline: Set to tomorrow
  - Assign to Group: Select a group
- [ ] Submit form
- [ ] Verify task appears in task list
- [ ] Verify success toast notification

#### 3.3 Edit Task
- [ ] Click "Edit" on any task
- [ ] Modify task details
- [ ] Save changes
- [ ] Verify changes are reflected in task list

### ✅ **Phase 4: Group Management (Admin Only)**

#### 4.1 View Groups
- [ ] Navigate to Groups page (admin only)
- [ ] Verify groups list displays
- [ ] Check group member counts

#### 4.2 Create Group
- [ ] Click "Create Group"
- [ ] Fill out form:
  - Name: `Test Integration Group`
  - Description: `Testing group creation`
- [ ] Submit form
- [ ] Verify group appears in list

#### 4.3 Add User to Group
- [ ] Click "Add User" on a group
- [ ] Enter a valid User ID (e.g., from sample data)
- [ ] Submit form
- [ ] Verify success message

### ✅ **Phase 5: Task Swaps**

#### 5.1 View Task Swaps
- [ ] Navigate to Task Swaps page
- [ ] Verify swap requests display with:
  - Task title
  - Requester and target user
  - Status badges
  - Action buttons (based on role and status)

#### 5.2 Admin Approve Swap (if pending)
- [ ] Login as admin
- [ ] Find a swap with "Pending Admin" status
- [ ] Click "Approve" or "Reject"
- [ ] Verify status change

#### 5.3 User Respond to Swap (if pending user)
- [ ] Login as target user
- [ ] Find a swap with "Pending User" status
- [ ] Click "Accept" or "Reject"
- [ ] Verify status change and task reassignment

### ✅ **Phase 6: Notifications**

#### 6.1 View Notifications
- [ ] Navigate to Notifications page
- [ ] Verify notifications display with:
  - Notification types and icons
  - Read/unread status
  - Timestamps
  - Statistics

#### 6.2 Mark as Read
- [ ] Click mark as read on individual notification
- [ ] Verify notification marked as read
- [ ] Test "Mark All Read" button

### ✅ **Phase 7: User Profile & Settings**

#### 7.1 User Profile
- [ ] Check user info in sidebar:
  - User initials avatar
  - Full name
  - User ID
  - Company information

#### 7.2 Logout
- [ ] Click "Logout" button
- [ ] Verify redirect to login page
- [ ] Verify session cleared

### ✅ **Phase 8: Responsive Design**

#### 8.1 Mobile Testing
- [ ] Resize browser to mobile width
- [ ] Test hamburger menu
- [ ] Verify sidebar overlay works
- [ ] Test all pages on mobile

#### 8.2 Tablet Testing
- [ ] Test medium screen sizes
- [ ] Verify grid layouts adapt
- [ ] Check navigation behavior

### ✅ **Phase 9: Error Handling**

#### 9.1 Network Errors
- [ ] Stop Django backend
- [ ] Try to perform actions
- [ ] Verify error messages appear
- [ ] Restart backend and verify recovery

#### 9.2 Invalid Data
- [ ] Try submitting forms with invalid data
- [ ] Verify validation messages
- [ ] Test required field validation

#### 9.3 Unauthorized Access
- [ ] Try accessing admin routes as regular user
- [ ] Verify proper redirects
- [ ] Test protected routes without login

## 🎯 **Success Criteria**

### ✅ **All Tests Must Pass**
- [ ] Authentication flow works completely
- [ ] All API endpoints respond correctly
- [ ] Real-time data updates work
- [ ] Role-based access control functions
- [ ] Responsive design works on all screen sizes
- [ ] Error handling is graceful
- [ ] Navigation is smooth and intuitive

### 📊 **Performance Checks**
- [ ] Pages load quickly (< 2 seconds)
- [ ] API responses are fast (< 500ms)
- [ ] No console errors
- [ ] Smooth animations and transitions

### 🔒 **Security Checks**
- [ ] JWT tokens work properly
- [ ] Automatic token refresh functions
- [ ] Protected routes are secure
- [ ] Admin-only features are restricted

## 🐛 **Common Issues & Solutions**

### CORS Errors
- Ensure Django server is running
- Check CORS settings in Django
- Verify frontend URL is whitelisted

### Authentication Issues
- Clear browser cookies/localStorage
- Check JWT token expiration
- Verify user credentials

### Data Not Loading
- Check network tab for API calls
- Verify backend endpoints are working
- Check React Query cache

## 🎉 **Integration Complete!**

If all tests pass, your TaskSphere platform is fully integrated and ready for:
- ✅ Production deployment
- ✅ User testing
- ✅ Feature development
- ✅ Team collaboration

**Frontend**: http://localhost:5173
**Backend**: http://127.0.0.1:8000
**Admin Panel**: http://127.0.0.1:8000/admin

Happy testing! 🚀
