/*! 🌼 daisyUI 5.0.45 - MIT License */ @layer utilities{.label{white-space:nowrap;color:color-mix(in oklab,currentColor 60%,transparent);align-items:center;gap:.375rem;display:inline-flex;&:has(input){cursor:pointer}&:is(.input>*,.select>*){white-space:nowrap;height:calc(100% - .5rem);font-size:inherit;align-items:center;padding-inline:.75rem;display:flex;&:first-child{border-inline-end:var(--border)solid color-mix(in oklab,currentColor 10%,#0000);margin-inline:-.75rem .75rem}&:last-child{border-inline-start:var(--border)solid color-mix(in oklab,currentColor 10%,#0000);margin-inline:.75rem -.75rem}}}.floating-label{display:block;position:relative;& input{display:block;&::placeholder{transition:top .1s ease-out,translate .1s ease-out,scale .1s ease-out,opacity .1s ease-out}}& textarea{&::placeholder{transition:top .1s ease-out,translate .1s ease-out,scale .1s ease-out,opacity .1s ease-out}}&>span{z-index:1;background-color:var(--color-base-100);opacity:0;inset-inline-start:.75rem;top:calc(var(--size-field,.25rem)*10/2);pointer-events:none;border-radius:2px;padding-inline:.25rem;font-size:.875rem;line-height:1;transition:top .1s ease-out,translate .1s ease-out,scale .1s ease-out,opacity .1s ease-out;position:absolute;translate:0 -50%}&:focus-within,&:not(:has(input:placeholder-shown,textarea:placeholder-shown)){& ::placeholder{opacity:0;pointer-events:auto;top:0;translate:-12.5% calc(-50% - .125em);scale:.75}&>span{opacity:1;pointer-events:auto;z-index:2;top:0;translate:-12.5% calc(-50% - .125em);scale:.75}}&:has(:disabled,[disabled]){&>span{opacity:0}}&:has(.input-xs,.select-xs,.textarea-xs) span{top:calc(var(--size-field,.25rem)*6/2);font-size:.6875rem}&:has(.input-sm,.select-sm,.textarea-sm) span{top:calc(var(--size-field,.25rem)*8/2);font-size:.75rem}&:has(.input-md,.select-md,.textarea-md) span{top:calc(var(--size-field,.25rem)*10/2);font-size:.875rem}&:has(.input-lg,.select-lg,.textarea-lg) span{top:calc(var(--size-field,.25rem)*12/2);font-size:1.125rem}&:has(.input-xl,.select-xl,.textarea-xl) span{top:calc(var(--size-field,.25rem)*14/2);font-size:1.375rem}}@media (width>=640px){.sm\:label{white-space:nowrap;color:color-mix(in oklab,currentColor 60%,transparent);align-items:center;gap:.375rem;display:inline-flex;&:has(input){cursor:pointer}&:is(.input>*,.select>*){white-space:nowrap;height:calc(100% - .5rem);font-size:inherit;align-items:center;padding-inline:.75rem;display:flex;&:first-child{border-inline-end:var(--border)solid color-mix(in oklab,currentColor 10%,#0000);margin-inline:-.75rem .75rem}&:last-child{border-inline-start:var(--border)solid color-mix(in oklab,currentColor 10%,#0000);margin-inline:.75rem -.75rem}}}.sm\:floating-label{display:block;position:relative;& input{display:block;&::placeholder{transition:top .1s ease-out,translate .1s ease-out,scale .1s ease-out,opacity .1s ease-out}}& textarea{&::placeholder{transition:top .1s ease-out,translate .1s ease-out,scale .1s ease-out,opacity .1s ease-out}}&>span{z-index:1;background-color:var(--color-base-100);opacity:0;inset-inline-start:.75rem;top:calc(var(--size-field,.25rem)*10/2);pointer-events:none;border-radius:2px;padding-inline:.25rem;font-size:.875rem;line-height:1;transition:top .1s ease-out,translate .1s ease-out,scale .1s ease-out,opacity .1s ease-out;position:absolute;translate:0 -50%}&:focus-within,&:not(:has(input:placeholder-shown,textarea:placeholder-shown)){& ::placeholder{opacity:0;pointer-events:auto;top:0;translate:-12.5% calc(-50% - .125em);scale:.75}&>span{opacity:1;pointer-events:auto;z-index:2;top:0;translate:-12.5% calc(-50% - .125em);scale:.75}}&:has(:disabled,[disabled]){&>span{opacity:0}}&:has(.input-xs,.select-xs,.textarea-xs) span{top:calc(var(--size-field,.25rem)*6/2);font-size:.6875rem}&:has(.input-sm,.select-sm,.textarea-sm) span{top:calc(var(--size-field,.25rem)*8/2);font-size:.75rem}&:has(.input-md,.select-md,.textarea-md) span{top:calc(var(--size-field,.25rem)*10/2);font-size:.875rem}&:has(.input-lg,.select-lg,.textarea-lg) span{top:calc(var(--size-field,.25rem)*12/2);font-size:1.125rem}&:has(.input-xl,.select-xl,.textarea-xl) span{top:calc(var(--size-field,.25rem)*14/2);font-size:1.375rem}}}@media (width>=768px){.md\:label{white-space:nowrap;color:color-mix(in oklab,currentColor 60%,transparent);align-items:center;gap:.375rem;display:inline-flex;&:has(input){cursor:pointer}&:is(.input>*,.select>*){white-space:nowrap;height:calc(100% - .5rem);font-size:inherit;align-items:center;padding-inline:.75rem;display:flex;&:first-child{border-inline-end:var(--border)solid color-mix(in oklab,currentColor 10%,#0000);margin-inline:-.75rem .75rem}&:last-child{border-inline-start:var(--border)solid color-mix(in oklab,currentColor 10%,#0000);margin-inline:.75rem -.75rem}}}.md\:floating-label{display:block;position:relative;& input{display:block;&::placeholder{transition:top .1s ease-out,translate .1s ease-out,scale .1s ease-out,opacity .1s ease-out}}& textarea{&::placeholder{transition:top .1s ease-out,translate .1s ease-out,scale .1s ease-out,opacity .1s ease-out}}&>span{z-index:1;background-color:var(--color-base-100);opacity:0;inset-inline-start:.75rem;top:calc(var(--size-field,.25rem)*10/2);pointer-events:none;border-radius:2px;padding-inline:.25rem;font-size:.875rem;line-height:1;transition:top .1s ease-out,translate .1s ease-out,scale .1s ease-out,opacity .1s ease-out;position:absolute;translate:0 -50%}&:focus-within,&:not(:has(input:placeholder-shown,textarea:placeholder-shown)){& ::placeholder{opacity:0;pointer-events:auto;top:0;translate:-12.5% calc(-50% - .125em);scale:.75}&>span{opacity:1;pointer-events:auto;z-index:2;top:0;translate:-12.5% calc(-50% - .125em);scale:.75}}&:has(:disabled,[disabled]){&>span{opacity:0}}&:has(.input-xs,.select-xs,.textarea-xs) span{top:calc(var(--size-field,.25rem)*6/2);font-size:.6875rem}&:has(.input-sm,.select-sm,.textarea-sm) span{top:calc(var(--size-field,.25rem)*8/2);font-size:.75rem}&:has(.input-md,.select-md,.textarea-md) span{top:calc(var(--size-field,.25rem)*10/2);font-size:.875rem}&:has(.input-lg,.select-lg,.textarea-lg) span{top:calc(var(--size-field,.25rem)*12/2);font-size:1.125rem}&:has(.input-xl,.select-xl,.textarea-xl) span{top:calc(var(--size-field,.25rem)*14/2);font-size:1.375rem}}}@media (width>=1024px){.lg\:label{white-space:nowrap;color:color-mix(in oklab,currentColor 60%,transparent);align-items:center;gap:.375rem;display:inline-flex;&:has(input){cursor:pointer}&:is(.input>*,.select>*){white-space:nowrap;height:calc(100% - .5rem);font-size:inherit;align-items:center;padding-inline:.75rem;display:flex;&:first-child{border-inline-end:var(--border)solid color-mix(in oklab,currentColor 10%,#0000);margin-inline:-.75rem .75rem}&:last-child{border-inline-start:var(--border)solid color-mix(in oklab,currentColor 10%,#0000);margin-inline:.75rem -.75rem}}}.lg\:floating-label{display:block;position:relative;& input{display:block;&::placeholder{transition:top .1s ease-out,translate .1s ease-out,scale .1s ease-out,opacity .1s ease-out}}& textarea{&::placeholder{transition:top .1s ease-out,translate .1s ease-out,scale .1s ease-out,opacity .1s ease-out}}&>span{z-index:1;background-color:var(--color-base-100);opacity:0;inset-inline-start:.75rem;top:calc(var(--size-field,.25rem)*10/2);pointer-events:none;border-radius:2px;padding-inline:.25rem;font-size:.875rem;line-height:1;transition:top .1s ease-out,translate .1s ease-out,scale .1s ease-out,opacity .1s ease-out;position:absolute;translate:0 -50%}&:focus-within,&:not(:has(input:placeholder-shown,textarea:placeholder-shown)){& ::placeholder{opacity:0;pointer-events:auto;top:0;translate:-12.5% calc(-50% - .125em);scale:.75}&>span{opacity:1;pointer-events:auto;z-index:2;top:0;translate:-12.5% calc(-50% - .125em);scale:.75}}&:has(:disabled,[disabled]){&>span{opacity:0}}&:has(.input-xs,.select-xs,.textarea-xs) span{top:calc(var(--size-field,.25rem)*6/2);font-size:.6875rem}&:has(.input-sm,.select-sm,.textarea-sm) span{top:calc(var(--size-field,.25rem)*8/2);font-size:.75rem}&:has(.input-md,.select-md,.textarea-md) span{top:calc(var(--size-field,.25rem)*10/2);font-size:.875rem}&:has(.input-lg,.select-lg,.textarea-lg) span{top:calc(var(--size-field,.25rem)*12/2);font-size:1.125rem}&:has(.input-xl,.select-xl,.textarea-xl) span{top:calc(var(--size-field,.25rem)*14/2);font-size:1.375rem}}}@media (width>=1280px){.xl\:label{white-space:nowrap;color:color-mix(in oklab,currentColor 60%,transparent);align-items:center;gap:.375rem;display:inline-flex;&:has(input){cursor:pointer}&:is(.input>*,.select>*){white-space:nowrap;height:calc(100% - .5rem);font-size:inherit;align-items:center;padding-inline:.75rem;display:flex;&:first-child{border-inline-end:var(--border)solid color-mix(in oklab,currentColor 10%,#0000);margin-inline:-.75rem .75rem}&:last-child{border-inline-start:var(--border)solid color-mix(in oklab,currentColor 10%,#0000);margin-inline:.75rem -.75rem}}}.xl\:floating-label{display:block;position:relative;& input{display:block;&::placeholder{transition:top .1s ease-out,translate .1s ease-out,scale .1s ease-out,opacity .1s ease-out}}& textarea{&::placeholder{transition:top .1s ease-out,translate .1s ease-out,scale .1s ease-out,opacity .1s ease-out}}&>span{z-index:1;background-color:var(--color-base-100);opacity:0;inset-inline-start:.75rem;top:calc(var(--size-field,.25rem)*10/2);pointer-events:none;border-radius:2px;padding-inline:.25rem;font-size:.875rem;line-height:1;transition:top .1s ease-out,translate .1s ease-out,scale .1s ease-out,opacity .1s ease-out;position:absolute;translate:0 -50%}&:focus-within,&:not(:has(input:placeholder-shown,textarea:placeholder-shown)){& ::placeholder{opacity:0;pointer-events:auto;top:0;translate:-12.5% calc(-50% - .125em);scale:.75}&>span{opacity:1;pointer-events:auto;z-index:2;top:0;translate:-12.5% calc(-50% - .125em);scale:.75}}&:has(:disabled,[disabled]){&>span{opacity:0}}&:has(.input-xs,.select-xs,.textarea-xs) span{top:calc(var(--size-field,.25rem)*6/2);font-size:.6875rem}&:has(.input-sm,.select-sm,.textarea-sm) span{top:calc(var(--size-field,.25rem)*8/2);font-size:.75rem}&:has(.input-md,.select-md,.textarea-md) span{top:calc(var(--size-field,.25rem)*10/2);font-size:.875rem}&:has(.input-lg,.select-lg,.textarea-lg) span{top:calc(var(--size-field,.25rem)*12/2);font-size:1.125rem}&:has(.input-xl,.select-xl,.textarea-xl) span{top:calc(var(--size-field,.25rem)*14/2);font-size:1.375rem}}}@media (width>=1536px){.\32 xl\:label{white-space:nowrap;color:color-mix(in oklab,currentColor 60%,transparent);align-items:center;gap:.375rem;display:inline-flex;&:has(input){cursor:pointer}&:is(.input>*,.select>*){white-space:nowrap;height:calc(100% - .5rem);font-size:inherit;align-items:center;padding-inline:.75rem;display:flex;&:first-child{border-inline-end:var(--border)solid color-mix(in oklab,currentColor 10%,#0000);margin-inline:-.75rem .75rem}&:last-child{border-inline-start:var(--border)solid color-mix(in oklab,currentColor 10%,#0000);margin-inline:.75rem -.75rem}}}.\32 xl\:floating-label{display:block;position:relative;& input{display:block;&::placeholder{transition:top .1s ease-out,translate .1s ease-out,scale .1s ease-out,opacity .1s ease-out}}& textarea{&::placeholder{transition:top .1s ease-out,translate .1s ease-out,scale .1s ease-out,opacity .1s ease-out}}&>span{z-index:1;background-color:var(--color-base-100);opacity:0;inset-inline-start:.75rem;top:calc(var(--size-field,.25rem)*10/2);pointer-events:none;border-radius:2px;padding-inline:.25rem;font-size:.875rem;line-height:1;transition:top .1s ease-out,translate .1s ease-out,scale .1s ease-out,opacity .1s ease-out;position:absolute;translate:0 -50%}&:focus-within,&:not(:has(input:placeholder-shown,textarea:placeholder-shown)){& ::placeholder{opacity:0;pointer-events:auto;top:0;translate:-12.5% calc(-50% - .125em);scale:.75}&>span{opacity:1;pointer-events:auto;z-index:2;top:0;translate:-12.5% calc(-50% - .125em);scale:.75}}&:has(:disabled,[disabled]){&>span{opacity:0}}&:has(.input-xs,.select-xs,.textarea-xs) span{top:calc(var(--size-field,.25rem)*6/2);font-size:.6875rem}&:has(.input-sm,.select-sm,.textarea-sm) span{top:calc(var(--size-field,.25rem)*8/2);font-size:.75rem}&:has(.input-md,.select-md,.textarea-md) span{top:calc(var(--size-field,.25rem)*10/2);font-size:.875rem}&:has(.input-lg,.select-lg,.textarea-lg) span{top:calc(var(--size-field,.25rem)*12/2);font-size:1.125rem}&:has(.input-xl,.select-xl,.textarea-xl) span{top:calc(var(--size-field,.25rem)*14/2);font-size:1.375rem}}}}