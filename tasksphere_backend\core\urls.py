from django.urls import path
from .views import (
    CompanyListView, CompanyDetailView,
    GroupListCreateView, GroupDetailView,
    add_user_to_group, remove_user_from_group,
    TaskListCreateView, TaskDetailView,
    TaskSwapListView, create_task_swap,
    admin_approve_swap, user_respond_swap
)

urlpatterns = [
    # Company URLs
    path('companies/', CompanyListView.as_view(), name='company-list'),
    path('companies/<int:pk>/', CompanyDetailView.as_view(), name='company-detail'),
    
    # Group URLs
    path('groups/', GroupListCreateView.as_view(), name='group-list-create'),
    path('groups/<int:pk>/', GroupDetailView.as_view(), name='group-detail'),
    path('groups/<int:group_id>/add-user/', add_user_to_group, name='add-user-to-group'),
    path('groups/<int:group_id>/remove-user/<str:user_id>/', remove_user_from_group, name='remove-user-from-group'),
    
    # Task URLs
    path('tasks/', TaskListCreateView.as_view(), name='task-list-create'),
    path('tasks/<int:pk>/', TaskDetailView.as_view(), name='task-detail'),
    
    # Task Swap URLs
    path('task-swaps/', TaskSwapListView.as_view(), name='task-swap-list'),
    path('task-swaps/create/', create_task_swap, name='create-task-swap'),
    path('task-swaps/<int:swap_id>/admin-approve/', admin_approve_swap, name='admin-approve-swap'),
    path('task-swaps/<int:swap_id>/user-respond/', user_respond_swap, name='user-respond-swap'),
]
