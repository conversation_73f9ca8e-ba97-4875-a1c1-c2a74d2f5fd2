import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { tasksAPI, taskSwapsAPI, notificationsAPI } from '../../services/api';
import ApiTest from '../test/ApiTest';
import {
  CheckSquare,
  Clock,
  AlertCircle,
  ArrowRightLeft,
  Bell,
  Plus,
  TrendingUp,
} from 'lucide-react';

const Dashboard = () => {
  const { user, getPrimaryCompany } = useAuth();
  const primaryCompany = getPrimaryCompany();

  // Fetch dashboard data
  const { data: tasksResponse = [] } = useQuery({
    queryKey: ['tasks', primaryCompany?.company_id],
    queryFn: () =>
      tasksAPI.getTasks({
        company_id: primaryCompany?.company_id,
        assigned_to_me: 'true',
      }),
    enabled: !!primaryCompany?.company_id,
  });

  const { data: taskSwapsResponse = [] } = useQuery({
    queryKey: ['taskSwaps', primaryCompany?.company_id],
    queryFn: () =>
      taskSwapsAPI.getTaskSwaps({
        company_id: primaryCompany?.company_id,
      }),
    enabled: !!primaryCompany?.company_id,
  });

  const { data: notificationsResponse = [] } = useQuery({
    queryKey: ['notifications'],
    queryFn: notificationsAPI.getNotifications,
  });

  // Handle paginated responses
  const tasks = Array.isArray(tasksResponse) ? tasksResponse : (tasksResponse?.results || []);
  const taskSwaps = Array.isArray(taskSwapsResponse) ? taskSwapsResponse : (taskSwapsResponse?.results || []);
  const notifications = Array.isArray(notificationsResponse) ? notificationsResponse : (notificationsResponse?.results || []);

  // Calculate statistics
  const taskStats = {
    total: tasks.length,
    pending: tasks.filter((task) => task.status === 'pending').length,
    inProgress: tasks.filter((task) => task.status === 'in_progress').length,
    completed: tasks.filter((task) => task.status === 'completed').length,
    overdue: tasks.filter((task) => task.is_overdue).length,
  };

  const swapStats = {
    total: taskSwaps.length,
    pendingAdmin: taskSwaps.filter((swap) => swap.status === 'pending_admin').length,
    pendingUser: taskSwaps.filter((swap) => swap.status === 'pending_user').length,
  };

  const unreadNotifications = notifications.filter((notif) => !notif.is_read).length;

  const StatCard = ({ title, value, icon: Icon, color, link }) => (
    <div className="card bg-base-100 shadow-sm">
      <div className="card-body p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-base-content/70 text-sm">{title}</p>
            <p className={`text-2xl font-bold ${color}`}>{value}</p>
          </div>
          <div className={`p-3 rounded-lg ${color} bg-opacity-10`}>
            <Icon className={`h-6 w-6 ${color}`} />
          </div>
        </div>
        {link && (
          <Link to={link} className="btn btn-ghost btn-sm mt-2 self-start">
            View All
          </Link>
        )}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-base-content">
            Welcome back, {user?.first_name}!
          </h1>
          <p className="text-base-content/70 mt-1">
            Here's what's happening with your tasks today.
          </p>
          {primaryCompany && (
            <div className="mt-2">
              <span className="badge badge-primary">
                Company: {primaryCompany.company_name} ({primaryCompany.company_code})
              </span>
            </div>
          )}
        </div>
        <Link to="/tasks/new" className="btn btn-primary">
          <Plus className="h-5 w-5 mr-2" />
          New Task
        </Link>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Tasks"
          value={taskStats.total}
          icon={CheckSquare}
          color="text-primary"
          link="/tasks"
        />
        <StatCard
          title="In Progress"
          value={taskStats.inProgress}
          icon={Clock}
          color="text-warning"
        />
        <StatCard
          title="Overdue"
          value={taskStats.overdue}
          icon={AlertCircle}
          color="text-error"
        />
        <StatCard
          title="Notifications"
          value={unreadNotifications}
          icon={Bell}
          color="text-info"
          link="/notifications"
        />
      </div>

      {/* Recent Tasks and Swaps */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Tasks */}
        <div className="card bg-base-100 shadow-sm">
          <div className="card-body">
            <div className="flex items-center justify-between mb-4">
              <h2 className="card-title">Recent Tasks</h2>
              <Link to="/tasks" className="btn btn-ghost btn-sm">
                View All
              </Link>
            </div>
            <div className="space-y-3">
              {tasks.slice(0, 5).map((task) => (
                <div
                  key={task.id}
                  className="flex items-center justify-between p-3 bg-base-200 rounded-lg"
                >
                  <div className="flex-1">
                    <h3 className="font-medium text-sm">{task.title}</h3>
                    <p className="text-xs text-base-content/70">
                      Due: {task.deadline ? new Date(task.deadline).toLocaleDateString() : 'No deadline'}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span
                      className={`badge badge-sm ${
                        task.status === 'completed'
                          ? 'badge-success'
                          : task.status === 'in_progress'
                          ? 'badge-warning'
                          : task.is_overdue
                          ? 'badge-error'
                          : 'badge-ghost'
                      }`}
                    >
                      {task.status.replace('_', ' ')}
                    </span>
                  </div>
                </div>
              ))}
              {tasks.length === 0 && (
                <div className="text-center py-8 text-base-content/70">
                  <CheckSquare className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>No tasks assigned to you yet.</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Recent Swaps */}
        <div className="card bg-base-100 shadow-sm">
          <div className="card-body">
            <div className="flex items-center justify-between mb-4">
              <h2 className="card-title">Task Swaps</h2>
              <Link to="/swaps" className="btn btn-ghost btn-sm">
                View All
              </Link>
            </div>
            <div className="space-y-3">
              {taskSwaps.slice(0, 5).map((swap) => (
                <div
                  key={swap.id}
                  className="flex items-center justify-between p-3 bg-base-200 rounded-lg"
                >
                  <div className="flex-1">
                    <h3 className="font-medium text-sm">{swap.task_title}</h3>
                    <p className="text-xs text-base-content/70">
                      {swap.requested_by_name} → {swap.requested_to_name}
                    </p>
                  </div>
                  <span
                    className={`badge badge-sm ${
                      swap.status === 'approved'
                        ? 'badge-success'
                        : swap.status === 'rejected'
                        ? 'badge-error'
                        : 'badge-warning'
                    }`}
                  >
                    {swap.status.replace('_', ' ')}
                  </span>
                </div>
              ))}
              {taskSwaps.length === 0 && (
                <div className="text-center py-8 text-base-content/70">
                  <ArrowRightLeft className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>No task swap requests.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* API Connection Test */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card bg-base-100 shadow-sm">
          <div className="card-body">
            <h2 className="card-title mb-4">Quick Actions</h2>
            <div className="grid grid-cols-1 gap-4">
              <Link to="/tasks" className="btn btn-outline">
                <CheckSquare className="h-5 w-5 mr-2" />
                View My Tasks
              </Link>
              <Link to="/swaps" className="btn btn-outline">
                <ArrowRightLeft className="h-5 w-5 mr-2" />
                Task Swaps
              </Link>
              <Link to="/notifications" className="btn btn-outline">
                <Bell className="h-5 w-5 mr-2" />
                Notifications
                {unreadNotifications > 0 && (
                  <span className="badge badge-error badge-sm ml-2">
                    {unreadNotifications}
                  </span>
                )}
              </Link>
            </div>
          </div>
        </div>

        {/* API Test Component */}
        <ApiTest />
      </div>
    </div>
  );
};

export default Dashboard;
