/*! 🌼 daisyUI 5.0.45 - MIT License */ @layer utilities{.checkbox{border:var(--border)solid var(--input-color,color-mix(in oklab,var(--color-base-content)20%,#0000));cursor:pointer;appearance:none;border-radius:var(--radius-selector);vertical-align:middle;color:var(--color-base-content);box-shadow:0 1px oklch(0% 0 0/calc(var(--depth)*.1))inset,0 0 #0000 inset,0 0 #0000;--size:calc(var(--size-selector,.25rem)*6);width:var(--size);height:var(--size);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);flex-shrink:0;padding:.25rem;transition:background-color .2s,box-shadow .2s;display:inline-block;position:relative;&:before{--tw-content:"";content:var(--tw-content);opacity:0;clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 80%,70% 80%,70% 100%);width:100%;height:100%;box-shadow:0px 3px 0 0px oklch(100% 0 0/calc(var(--depth)*.1))inset;background-color:currentColor;font-size:1rem;line-height:.75;transition:clip-path .3s .1s,opacity .1s .1s,rotate .3s .1s,translate .3s .1s;display:block;rotate:45deg}&:focus-visible{outline:2px solid var(--input-color,currentColor);outline-offset:2px}&:checked,&[aria-checked=true]{background-color:var(--input-color,#0000);box-shadow:0 0 #0000 inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px oklch(0% 0 0/calc(var(--depth)*.1));&:before{clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 0%,70% 0%,70% 100%);opacity:1}@media (forced-colors:active){&:before{--tw-content:"✔︎";clip-path:none;background-color:#0000;rotate:none}}@media print{&:before{--tw-content:"✔︎";clip-path:none;background-color:#0000;rotate:none}}}&:indeterminate{&:before{opacity:1;clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 80%,80% 80%,80% 100%);translate:0 -35%;rotate:none}}}.checkbox-primary{color:var(--color-primary-content);--input-color:var(--color-primary)}.checkbox-secondary{color:var(--color-secondary-content);--input-color:var(--color-secondary)}.checkbox-accent{color:var(--color-accent-content);--input-color:var(--color-accent)}.checkbox-neutral{color:var(--color-neutral-content);--input-color:var(--color-neutral)}.checkbox-info{color:var(--color-info-content);--input-color:var(--color-info)}.checkbox-success{color:var(--color-success-content);--input-color:var(--color-success)}.checkbox-warning{color:var(--color-warning-content);--input-color:var(--color-warning)}.checkbox-error{color:var(--color-error-content);--input-color:var(--color-error)}.checkbox:disabled{cursor:not-allowed;opacity:.2}.checkbox-xs{--size:calc(var(--size-selector,.25rem)*4);padding:.125rem}.checkbox-sm{--size:calc(var(--size-selector,.25rem)*5);padding:.1875rem}.checkbox-md{--size:calc(var(--size-selector,.25rem)*6);padding:.25rem}.checkbox-lg{--size:calc(var(--size-selector,.25rem)*7);padding:.3125rem}.checkbox-xl{--size:calc(var(--size-selector,.25rem)*8);padding:.375rem}@media (width>=640px){.sm\:checkbox{border:var(--border)solid var(--input-color,color-mix(in oklab,var(--color-base-content)20%,#0000));cursor:pointer;appearance:none;border-radius:var(--radius-selector);vertical-align:middle;color:var(--color-base-content);box-shadow:0 1px oklch(0% 0 0/calc(var(--depth)*.1))inset,0 0 #0000 inset,0 0 #0000;--size:calc(var(--size-selector,.25rem)*6);width:var(--size);height:var(--size);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);flex-shrink:0;padding:.25rem;transition:background-color .2s,box-shadow .2s;display:inline-block;position:relative;&:before{--tw-content:"";content:var(--tw-content);opacity:0;clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 80%,70% 80%,70% 100%);width:100%;height:100%;box-shadow:0px 3px 0 0px oklch(100% 0 0/calc(var(--depth)*.1))inset;background-color:currentColor;font-size:1rem;line-height:.75;transition:clip-path .3s .1s,opacity .1s .1s,rotate .3s .1s,translate .3s .1s;display:block;rotate:45deg}&:focus-visible{outline:2px solid var(--input-color,currentColor);outline-offset:2px}&:checked,&[aria-checked=true]{background-color:var(--input-color,#0000);box-shadow:0 0 #0000 inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px oklch(0% 0 0/calc(var(--depth)*.1));&:before{clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 0%,70% 0%,70% 100%);opacity:1}@media (forced-colors:active){&:before{--tw-content:"✔︎";clip-path:none;background-color:#0000;rotate:none}}@media print{&:before{--tw-content:"✔︎";clip-path:none;background-color:#0000;rotate:none}}}&:indeterminate{&:before{opacity:1;clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 80%,80% 80%,80% 100%);translate:0 -35%;rotate:none}}}.sm\:checkbox-primary{color:var(--color-primary-content);--input-color:var(--color-primary)}.sm\:checkbox-secondary{color:var(--color-secondary-content);--input-color:var(--color-secondary)}.sm\:checkbox-accent{color:var(--color-accent-content);--input-color:var(--color-accent)}.sm\:checkbox-neutral{color:var(--color-neutral-content);--input-color:var(--color-neutral)}.sm\:checkbox-info{color:var(--color-info-content);--input-color:var(--color-info)}.sm\:checkbox-success{color:var(--color-success-content);--input-color:var(--color-success)}.sm\:checkbox-warning{color:var(--color-warning-content);--input-color:var(--color-warning)}.sm\:checkbox-error{color:var(--color-error-content);--input-color:var(--color-error)}.sm\:checkbox:disabled{cursor:not-allowed;opacity:.2}.sm\:checkbox-xs{--size:calc(var(--size-selector,.25rem)*4);padding:.125rem}.sm\:checkbox-sm{--size:calc(var(--size-selector,.25rem)*5);padding:.1875rem}.sm\:checkbox-md{--size:calc(var(--size-selector,.25rem)*6);padding:.25rem}.sm\:checkbox-lg{--size:calc(var(--size-selector,.25rem)*7);padding:.3125rem}.sm\:checkbox-xl{--size:calc(var(--size-selector,.25rem)*8);padding:.375rem}}@media (width>=768px){.md\:checkbox{border:var(--border)solid var(--input-color,color-mix(in oklab,var(--color-base-content)20%,#0000));cursor:pointer;appearance:none;border-radius:var(--radius-selector);vertical-align:middle;color:var(--color-base-content);box-shadow:0 1px oklch(0% 0 0/calc(var(--depth)*.1))inset,0 0 #0000 inset,0 0 #0000;--size:calc(var(--size-selector,.25rem)*6);width:var(--size);height:var(--size);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);flex-shrink:0;padding:.25rem;transition:background-color .2s,box-shadow .2s;display:inline-block;position:relative;&:before{--tw-content:"";content:var(--tw-content);opacity:0;clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 80%,70% 80%,70% 100%);width:100%;height:100%;box-shadow:0px 3px 0 0px oklch(100% 0 0/calc(var(--depth)*.1))inset;background-color:currentColor;font-size:1rem;line-height:.75;transition:clip-path .3s .1s,opacity .1s .1s,rotate .3s .1s,translate .3s .1s;display:block;rotate:45deg}&:focus-visible{outline:2px solid var(--input-color,currentColor);outline-offset:2px}&:checked,&[aria-checked=true]{background-color:var(--input-color,#0000);box-shadow:0 0 #0000 inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px oklch(0% 0 0/calc(var(--depth)*.1));&:before{clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 0%,70% 0%,70% 100%);opacity:1}@media (forced-colors:active){&:before{--tw-content:"✔︎";clip-path:none;background-color:#0000;rotate:none}}@media print{&:before{--tw-content:"✔︎";clip-path:none;background-color:#0000;rotate:none}}}&:indeterminate{&:before{opacity:1;clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 80%,80% 80%,80% 100%);translate:0 -35%;rotate:none}}}.md\:checkbox-primary{color:var(--color-primary-content);--input-color:var(--color-primary)}.md\:checkbox-secondary{color:var(--color-secondary-content);--input-color:var(--color-secondary)}.md\:checkbox-accent{color:var(--color-accent-content);--input-color:var(--color-accent)}.md\:checkbox-neutral{color:var(--color-neutral-content);--input-color:var(--color-neutral)}.md\:checkbox-info{color:var(--color-info-content);--input-color:var(--color-info)}.md\:checkbox-success{color:var(--color-success-content);--input-color:var(--color-success)}.md\:checkbox-warning{color:var(--color-warning-content);--input-color:var(--color-warning)}.md\:checkbox-error{color:var(--color-error-content);--input-color:var(--color-error)}.md\:checkbox:disabled{cursor:not-allowed;opacity:.2}.md\:checkbox-xs{--size:calc(var(--size-selector,.25rem)*4);padding:.125rem}.md\:checkbox-sm{--size:calc(var(--size-selector,.25rem)*5);padding:.1875rem}.md\:checkbox-md{--size:calc(var(--size-selector,.25rem)*6);padding:.25rem}.md\:checkbox-lg{--size:calc(var(--size-selector,.25rem)*7);padding:.3125rem}.md\:checkbox-xl{--size:calc(var(--size-selector,.25rem)*8);padding:.375rem}}@media (width>=1024px){.lg\:checkbox{border:var(--border)solid var(--input-color,color-mix(in oklab,var(--color-base-content)20%,#0000));cursor:pointer;appearance:none;border-radius:var(--radius-selector);vertical-align:middle;color:var(--color-base-content);box-shadow:0 1px oklch(0% 0 0/calc(var(--depth)*.1))inset,0 0 #0000 inset,0 0 #0000;--size:calc(var(--size-selector,.25rem)*6);width:var(--size);height:var(--size);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);flex-shrink:0;padding:.25rem;transition:background-color .2s,box-shadow .2s;display:inline-block;position:relative;&:before{--tw-content:"";content:var(--tw-content);opacity:0;clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 80%,70% 80%,70% 100%);width:100%;height:100%;box-shadow:0px 3px 0 0px oklch(100% 0 0/calc(var(--depth)*.1))inset;background-color:currentColor;font-size:1rem;line-height:.75;transition:clip-path .3s .1s,opacity .1s .1s,rotate .3s .1s,translate .3s .1s;display:block;rotate:45deg}&:focus-visible{outline:2px solid var(--input-color,currentColor);outline-offset:2px}&:checked,&[aria-checked=true]{background-color:var(--input-color,#0000);box-shadow:0 0 #0000 inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px oklch(0% 0 0/calc(var(--depth)*.1));&:before{clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 0%,70% 0%,70% 100%);opacity:1}@media (forced-colors:active){&:before{--tw-content:"✔︎";clip-path:none;background-color:#0000;rotate:none}}@media print{&:before{--tw-content:"✔︎";clip-path:none;background-color:#0000;rotate:none}}}&:indeterminate{&:before{opacity:1;clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 80%,80% 80%,80% 100%);translate:0 -35%;rotate:none}}}.lg\:checkbox-primary{color:var(--color-primary-content);--input-color:var(--color-primary)}.lg\:checkbox-secondary{color:var(--color-secondary-content);--input-color:var(--color-secondary)}.lg\:checkbox-accent{color:var(--color-accent-content);--input-color:var(--color-accent)}.lg\:checkbox-neutral{color:var(--color-neutral-content);--input-color:var(--color-neutral)}.lg\:checkbox-info{color:var(--color-info-content);--input-color:var(--color-info)}.lg\:checkbox-success{color:var(--color-success-content);--input-color:var(--color-success)}.lg\:checkbox-warning{color:var(--color-warning-content);--input-color:var(--color-warning)}.lg\:checkbox-error{color:var(--color-error-content);--input-color:var(--color-error)}.lg\:checkbox:disabled{cursor:not-allowed;opacity:.2}.lg\:checkbox-xs{--size:calc(var(--size-selector,.25rem)*4);padding:.125rem}.lg\:checkbox-sm{--size:calc(var(--size-selector,.25rem)*5);padding:.1875rem}.lg\:checkbox-md{--size:calc(var(--size-selector,.25rem)*6);padding:.25rem}.lg\:checkbox-lg{--size:calc(var(--size-selector,.25rem)*7);padding:.3125rem}.lg\:checkbox-xl{--size:calc(var(--size-selector,.25rem)*8);padding:.375rem}}@media (width>=1280px){.xl\:checkbox{border:var(--border)solid var(--input-color,color-mix(in oklab,var(--color-base-content)20%,#0000));cursor:pointer;appearance:none;border-radius:var(--radius-selector);vertical-align:middle;color:var(--color-base-content);box-shadow:0 1px oklch(0% 0 0/calc(var(--depth)*.1))inset,0 0 #0000 inset,0 0 #0000;--size:calc(var(--size-selector,.25rem)*6);width:var(--size);height:var(--size);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);flex-shrink:0;padding:.25rem;transition:background-color .2s,box-shadow .2s;display:inline-block;position:relative;&:before{--tw-content:"";content:var(--tw-content);opacity:0;clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 80%,70% 80%,70% 100%);width:100%;height:100%;box-shadow:0px 3px 0 0px oklch(100% 0 0/calc(var(--depth)*.1))inset;background-color:currentColor;font-size:1rem;line-height:.75;transition:clip-path .3s .1s,opacity .1s .1s,rotate .3s .1s,translate .3s .1s;display:block;rotate:45deg}&:focus-visible{outline:2px solid var(--input-color,currentColor);outline-offset:2px}&:checked,&[aria-checked=true]{background-color:var(--input-color,#0000);box-shadow:0 0 #0000 inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px oklch(0% 0 0/calc(var(--depth)*.1));&:before{clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 0%,70% 0%,70% 100%);opacity:1}@media (forced-colors:active){&:before{--tw-content:"✔︎";clip-path:none;background-color:#0000;rotate:none}}@media print{&:before{--tw-content:"✔︎";clip-path:none;background-color:#0000;rotate:none}}}&:indeterminate{&:before{opacity:1;clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 80%,80% 80%,80% 100%);translate:0 -35%;rotate:none}}}.xl\:checkbox-primary{color:var(--color-primary-content);--input-color:var(--color-primary)}.xl\:checkbox-secondary{color:var(--color-secondary-content);--input-color:var(--color-secondary)}.xl\:checkbox-accent{color:var(--color-accent-content);--input-color:var(--color-accent)}.xl\:checkbox-neutral{color:var(--color-neutral-content);--input-color:var(--color-neutral)}.xl\:checkbox-info{color:var(--color-info-content);--input-color:var(--color-info)}.xl\:checkbox-success{color:var(--color-success-content);--input-color:var(--color-success)}.xl\:checkbox-warning{color:var(--color-warning-content);--input-color:var(--color-warning)}.xl\:checkbox-error{color:var(--color-error-content);--input-color:var(--color-error)}.xl\:checkbox:disabled{cursor:not-allowed;opacity:.2}.xl\:checkbox-xs{--size:calc(var(--size-selector,.25rem)*4);padding:.125rem}.xl\:checkbox-sm{--size:calc(var(--size-selector,.25rem)*5);padding:.1875rem}.xl\:checkbox-md{--size:calc(var(--size-selector,.25rem)*6);padding:.25rem}.xl\:checkbox-lg{--size:calc(var(--size-selector,.25rem)*7);padding:.3125rem}.xl\:checkbox-xl{--size:calc(var(--size-selector,.25rem)*8);padding:.375rem}}@media (width>=1536px){.\32 xl\:checkbox{border:var(--border)solid var(--input-color,color-mix(in oklab,var(--color-base-content)20%,#0000));cursor:pointer;appearance:none;border-radius:var(--radius-selector);vertical-align:middle;color:var(--color-base-content);box-shadow:0 1px oklch(0% 0 0/calc(var(--depth)*.1))inset,0 0 #0000 inset,0 0 #0000;--size:calc(var(--size-selector,.25rem)*6);width:var(--size);height:var(--size);background-size:auto,calc(var(--noise)*100%);background-image:none,var(--fx-noise);flex-shrink:0;padding:.25rem;transition:background-color .2s,box-shadow .2s;display:inline-block;position:relative;&:before{--tw-content:"";content:var(--tw-content);opacity:0;clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 80%,70% 80%,70% 100%);width:100%;height:100%;box-shadow:0px 3px 0 0px oklch(100% 0 0/calc(var(--depth)*.1))inset;background-color:currentColor;font-size:1rem;line-height:.75;transition:clip-path .3s .1s,opacity .1s .1s,rotate .3s .1s,translate .3s .1s;display:block;rotate:45deg}&:focus-visible{outline:2px solid var(--input-color,currentColor);outline-offset:2px}&:checked,&[aria-checked=true]{background-color:var(--input-color,#0000);box-shadow:0 0 #0000 inset,0 8px 0 -4px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px oklch(0% 0 0/calc(var(--depth)*.1));&:before{clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 0%,70% 0%,70% 100%);opacity:1}@media (forced-colors:active){&:before{--tw-content:"✔︎";clip-path:none;background-color:#0000;rotate:none}}@media print{&:before{--tw-content:"✔︎";clip-path:none;background-color:#0000;rotate:none}}}&:indeterminate{&:before{opacity:1;clip-path:polygon(20% 100%,20% 80%,50% 80%,50% 80%,80% 80%,80% 100%);translate:0 -35%;rotate:none}}}.\32 xl\:checkbox-primary{color:var(--color-primary-content);--input-color:var(--color-primary)}.\32 xl\:checkbox-secondary{color:var(--color-secondary-content);--input-color:var(--color-secondary)}.\32 xl\:checkbox-accent{color:var(--color-accent-content);--input-color:var(--color-accent)}.\32 xl\:checkbox-neutral{color:var(--color-neutral-content);--input-color:var(--color-neutral)}.\32 xl\:checkbox-info{color:var(--color-info-content);--input-color:var(--color-info)}.\32 xl\:checkbox-success{color:var(--color-success-content);--input-color:var(--color-success)}.\32 xl\:checkbox-warning{color:var(--color-warning-content);--input-color:var(--color-warning)}.\32 xl\:checkbox-error{color:var(--color-error-content);--input-color:var(--color-error)}.\32 xl\:checkbox:disabled{cursor:not-allowed;opacity:.2}.\32 xl\:checkbox-xs{--size:calc(var(--size-selector,.25rem)*4);padding:.125rem}.\32 xl\:checkbox-sm{--size:calc(var(--size-selector,.25rem)*5);padding:.1875rem}.\32 xl\:checkbox-md{--size:calc(var(--size-selector,.25rem)*6);padding:.25rem}.\32 xl\:checkbox-lg{--size:calc(var(--size-selector,.25rem)*7);padding:.3125rem}.\32 xl\:checkbox-xl{--size:calc(var(--size-selector,.25rem)*8);padding:.375rem}}}