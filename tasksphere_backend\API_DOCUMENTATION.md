# TaskSphere Backend API Documentation

## Overview
TaskSphere is a multi-tenant task management platform that allows multiple companies to manage and track internal tasks efficiently. This document provides comprehensive API documentation for the Django REST Framework backend.

## Base URL
```
http://127.0.0.1:8000/api/
```

## Authentication
The API uses <PERSON>WT (JSON Web Token) authentication. Include the access token in the Authorization header:
```
Authorization: Bearer <access_token>
```

## Sample Data
After running `python create_sample_data.py`, you can use these credentials for testing:

**Company Code:** HCGUBO (TechCorp Solutions)

**Test Users:**
- Admin: <EMAIL> (ID: TSEHZCAY) - Password: admin123
- Alice: <EMAIL> (ID: QGUIU1R9) - Password: user123  
- Bob: <EMAIL> (ID: 5UUEEMZD) - Password: user123
- Carol: <EMAIL> (ID: SZHDQXJR) - Password: user123

---

## Authentication Endpoints

### 1. User Registration
**POST** `/auth/register/`

Register a new user and join a company.

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "username": "username",
    "first_name": "John",
    "last_name": "Doe",
    "phone_number": "+1234567890",
    "password": "securepassword",
    "password_confirm": "securepassword",
    "company_code": "HCGUBO"
}
```

**Response:**
```json
{
    "message": "User registered successfully",
    "user": {
        "user_id": "ABC12345",
        "email": "<EMAIL>",
        "username": "username",
        "first_name": "John",
        "last_name": "Doe",
        "phone_number": "+1234567890",
        "date_joined": "2025-07-10T23:49:21.123456Z",
        "companies": [...]
    },
    "tokens": {
        "refresh": "refresh_token_here",
        "access": "access_token_here"
    }
}
```

### 2. User Login
**POST** `/auth/login/`

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "password": "admin123"
}
```

**Response:**
```json
{
    "message": "Login successful",
    "user": {...},
    "tokens": {
        "refresh": "refresh_token_here",
        "access": "access_token_here"
    }
}
```

### 3. User Profile
**GET** `/auth/profile/`

Get current user profile information.

**Headers:** `Authorization: Bearer <access_token>`

### 4. Change Password
**POST** `/auth/change-password/`

**Request Body:**
```json
{
    "old_password": "oldpassword",
    "new_password": "newpassword",
    "new_password_confirm": "newpassword"
}
```

### 5. Logout
**POST** `/auth/logout/`

**Request Body:**
```json
{
    "refresh_token": "refresh_token_here"
}
```

### 6. Token Refresh
**POST** `/auth/token/refresh/`

**Request Body:**
```json
{
    "refresh": "refresh_token_here"
}
```

---

## Company Management

### 1. List Companies
**GET** `/companies/`

List companies where the user is a member.

### 2. Company Details
**GET** `/companies/{id}/`

Get details of a specific company.

---

## Group Management

### 1. List/Create Groups
**GET/POST** `/groups/?company_id={company_id}`

List groups in a company or create a new group (admin only).

**POST Request Body:**
```json
{
    "name": "Development Team",
    "description": "Software development team",
    "company": 1
}
```

### 2. Group Details
**GET/PUT/DELETE** `/groups/{id}/`

Retrieve, update, or delete a group (admin only for modifications).

### 3. Add User to Group
**POST** `/groups/{group_id}/add-user/`

Add a user to a group by their User ID (admin only).

**Request Body:**
```json
{
    "user_id": "QGUIU1R9"
}
```

### 4. Remove User from Group
**DELETE** `/groups/{group_id}/remove-user/{user_id}/`

Remove a user from a group (admin only).

---

## Task Management

### 1. List/Create Tasks
**GET/POST** `/tasks/?company_id={company_id}&status={status}&assigned_to_me={true/false}`

List tasks with optional filters or create a new task (admin only for creation).

**POST Request Body:**
```json
{
    "title": "Implement User Authentication",
    "description": "Develop JWT-based authentication system",
    "company": 1,
    "assigned_to": 2,
    "assigned_group": 1,
    "priority": "high",
    "deadline": "2025-07-17T23:59:59Z"
}
```

### 2. Task Details
**GET/PUT/DELETE** `/tasks/{id}/`

Retrieve, update, or delete a task.

**Status Update (Users can update their own tasks):**
```json
{
    "status": "completed"
}
```

**Full Update (Admin only):**
```json
{
    "title": "Updated Task Title",
    "description": "Updated description",
    "priority": "medium",
    "deadline": "2025-07-20T23:59:59Z"
}
```

---

## Task Swap Management

### 1. List Task Swaps
**GET** `/task-swaps/?company_id={company_id}&status={status}`

List task swap requests. Admins see all swaps, users see only their own.

### 2. Create Task Swap
**POST** `/task-swaps/create/`

Create a new task swap request.

**Request Body:**
```json
{
    "task_id": 1,
    "requested_to_user_id": "5UUEEMZD",
    "reason": "I have expertise in this area"
}
```

### 3. Admin Approve Swap
**POST** `/task-swaps/{swap_id}/admin-approve/`

Admin approve or reject a swap request.

**Request Body:**
```json
{
    "action": "approve"
}
```
or
```json
{
    "action": "reject"
}
```

### 4. User Respond to Swap
**POST** `/task-swaps/{swap_id}/user-respond/`

User accept or reject a swap request.

**Request Body:**
```json
{
    "action": "accept"
}
```
or
```json
{
    "action": "reject"
}
```

---

## Notifications

### 1. List Notifications
**GET** `/notifications/`

Get all notifications for the current user.

### 2. Mark Notification as Read
**POST** `/notifications/{notification_id}/read/`

Mark a specific notification as read.

### 3. Mark All Notifications as Read
**POST** `/notifications/mark-all-read/`

Mark all notifications as read for the current user.

---

## Task Swap Workflow

1. **User Initiates Swap**: User A requests to swap their task with User B
2. **Admin Approval**: Request goes to company admin for approval
3. **User Response**: If admin approves, User B can accept or reject
4. **Task Reassignment**: If both approve, task is automatically reassigned

**Swap Statuses:**
- `pending_admin`: Waiting for admin approval
- `pending_user`: Waiting for target user response
- `approved`: Swap completed successfully
- `rejected`: Swap rejected by admin or user

---

## Error Responses

All endpoints return appropriate HTTP status codes and error messages:

```json
{
    "error": "Error message description"
}
```

Common status codes:
- 200: Success
- 201: Created
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 500: Internal Server Error
