import { useQuery } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { tasksAPI, taskSwapsAPI, notificationsAPI } from '../../services/api';
import {
  CheckSquare,
  Clock,
  AlertCircle,
  ArrowRightLeft,
  Bell,
  Calendar,
  TrendingUp,
  User,
} from 'lucide-react';

const UserDashboard = () => {
  const { user, getPrimaryCompany } = useAuth();
  const primaryCompany = getPrimaryCompany();

  // Fetch user dashboard data
  const { data: myTasksResponse = [] } = useQuery({
    queryKey: ['user-tasks', primaryCompany?.company_id],
    queryFn: () =>
      tasksAPI.getTasks({
        company_id: primaryCompany?.company_id,
        assigned_to_me: 'true',
      }),
    enabled: !!primaryCompany?.company_id,
  });

  const { data: mySwapsResponse = [] } = useQuery({
    queryKey: ['user-taskSwaps', primaryCompany?.company_id],
    queryFn: () =>
      taskSwapsAPI.getTaskSwaps({
        company_id: primaryCompany?.company_id,
      }),
    enabled: !!primaryCompany?.company_id,
  });

  const { data: notificationsResponse = [] } = useQuery({
    queryKey: ['user-notifications'],
    queryFn: notificationsAPI.getNotifications,
  });

  // Handle paginated responses
  const myTasks = Array.isArray(myTasksResponse) ? myTasksResponse : (myTasksResponse?.results || []);
  const mySwaps = Array.isArray(mySwapsResponse) ? mySwapsResponse : (mySwapsResponse?.results || []);
  const notifications = Array.isArray(notificationsResponse) ? notificationsResponse : (notificationsResponse?.results || []);

  // Calculate user statistics
  const userStats = {
    totalTasks: myTasks.length,
    pendingTasks: myTasks.filter((task) => task.status === 'pending').length,
    inProgressTasks: myTasks.filter((task) => task.status === 'in_progress').length,
    completedTasks: myTasks.filter((task) => task.status === 'completed').length,
    overdueTasks: myTasks.filter((task) => task.is_overdue).length,
    pendingSwaps: mySwaps.filter((swap) => swap.status === 'pending_user' && swap.requested_to_name === `${user?.first_name} ${user?.last_name}`).length,
    unreadNotifications: notifications.filter((notif) => !notif.is_read).length,
  };

  const StatCard = ({ title, value, icon: Icon, color, link, description }) => (
    <div className="card bg-white shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
      <div className="card-body p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-gray-600 text-sm font-medium">{title}</p>
            <p className={`text-3xl font-bold ${color} mt-1`}>{value}</p>
            {description && (
              <p className="text-xs text-gray-500 mt-1">{description}</p>
            )}
          </div>
          <div className={`p-3 rounded-lg ${color} bg-opacity-10`}>
            <Icon className={`h-8 w-8 ${color}`} />
          </div>
        </div>
        {link && (
          <Link to={link} className="btn btn-outline btn-sm mt-4 self-start">
            View All
          </Link>
        )}
      </div>
    </div>
  );

  const getTaskPriorityColor = (priority) => {
    const colorMap = {
      low: 'text-green-600',
      medium: 'text-yellow-600',
      high: 'text-orange-600',
      urgent: 'text-red-600',
    };
    return colorMap[priority] || 'text-gray-600';
  };

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold text-gray-800 flex items-center">
            <User className="h-8 w-8 mr-3 text-green-600" />
            Welcome back, {user?.first_name}!
          </h1>
          <p className="text-gray-600 mt-2 text-lg">
            Here's what's happening with your tasks today.
          </p>
          {primaryCompany && (
            <div className="mt-3">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                Company: {primaryCompany.company_name}
              </span>
            </div>
          )}
        </div>
        <div className="flex space-x-3">
          <Link to="/user/tasks" className="btn btn-primary">
            <CheckSquare className="h-5 w-5 mr-2" />
            View My Tasks
          </Link>
        </div>
      </div>

      {/* User Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="My Tasks"
          value={userStats.totalTasks}
          icon={CheckSquare}
          color="text-green-600"
          link="/user/tasks"
          description="Total assigned to me"
        />
        <StatCard
          title="In Progress"
          value={userStats.inProgressTasks}
          icon={Clock}
          color="text-blue-600"
          description="Currently working on"
        />
        <StatCard
          title="Overdue"
          value={userStats.overdueTasks}
          icon={AlertCircle}
          color="text-red-600"
          description="Need immediate attention"
        />
        <StatCard
          title="Notifications"
          value={userStats.unreadNotifications}
          icon={Bell}
          color="text-purple-600"
          link="/user/notifications"
          description="Unread messages"
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* My Tasks */}
        <div className="card bg-white shadow-sm border border-gray-200">
          <div className="card-body">
            <div className="flex items-center justify-between mb-6">
              <h2 className="card-title text-xl text-gray-800">My Tasks</h2>
              <Link to="/user/tasks" className="btn btn-ghost btn-sm text-green-600">
                View All
              </Link>
            </div>
            <div className="space-y-4">
              {myTasks.slice(0, 5).map((task) => (
                <div
                  key={task.id}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-800">{task.title}</h3>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className={`text-xs font-medium ${getTaskPriorityColor(task.priority)}`}>
                        {task.priority.toUpperCase()}
                      </span>
                      {task.deadline && (
                        <span className="text-xs text-gray-500">
                          Due: {new Date(task.deadline).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span
                      className={`badge badge-sm ${
                        task.status === 'completed'
                          ? 'badge-success'
                          : task.status === 'in_progress'
                          ? 'badge-warning'
                          : task.is_overdue
                          ? 'badge-error'
                          : 'badge-ghost'
                      }`}
                    >
                      {task.status.replace('_', ' ')}
                    </span>
                  </div>
                </div>
              ))}
              {myTasks.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <CheckSquare className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>No tasks assigned to you yet.</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Task Swaps & Notifications */}
        <div className="space-y-6">
          {/* Pending Swaps */}
          <div className="card bg-white shadow-sm border border-gray-200">
            <div className="card-body">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-800">Pending Swaps</h3>
                <Link to="/user/swaps" className="btn btn-ghost btn-sm text-blue-600">
                  View All
                </Link>
              </div>
              <div className="space-y-3">
                {mySwaps
                  .filter((swap) => swap.status === 'pending_user' && swap.requested_to_name === `${user?.first_name} ${user?.last_name}`)
                  .slice(0, 3)
                  .map((swap) => (
                    <div
                      key={swap.id}
                      className="flex items-center justify-between p-3 bg-blue-50 rounded-lg"
                    >
                      <div className="flex-1">
                        <h4 className="font-medium text-sm text-gray-800">{swap.task_title}</h4>
                        <p className="text-xs text-gray-600">From: {swap.requested_by_name}</p>
                      </div>
                      <span className="badge badge-info badge-sm">
                        Pending
                      </span>
                    </div>
                  ))}
                {userStats.pendingSwaps === 0 && (
                  <div className="text-center py-4 text-gray-500">
                    <ArrowRightLeft className="h-8 w-8 mx-auto mb-1 opacity-50" />
                    <p className="text-sm">No pending swaps.</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Recent Notifications */}
          <div className="card bg-white shadow-sm border border-gray-200">
            <div className="card-body">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-800">Recent Notifications</h3>
                <Link to="/user/notifications" className="btn btn-ghost btn-sm text-purple-600">
                  View All
                </Link>
              </div>
              <div className="space-y-3">
                {notifications.slice(0, 3).map((notification) => (
                  <div
                    key={notification.id}
                    className={`flex items-start space-x-3 p-3 rounded-lg ${
                      notification.is_read ? 'bg-gray-50' : 'bg-purple-50'
                    }`}
                  >
                    <Bell className="h-4 w-4 text-purple-600 mt-0.5" />
                    <div className="flex-1">
                      <h4 className={`font-medium text-sm ${notification.is_read ? 'text-gray-600' : 'text-gray-800'}`}>
                        {notification.title}
                      </h4>
                      <p className="text-xs text-gray-500">
                        {new Date(notification.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    {!notification.is_read && (
                      <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                    )}
                  </div>
                ))}
                {notifications.length === 0 && (
                  <div className="text-center py-4 text-gray-500">
                    <Bell className="h-8 w-8 mx-auto mb-1 opacity-50" />
                    <p className="text-sm">No notifications.</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card bg-white shadow-sm border border-gray-200">
        <div className="card-body">
          <h2 className="card-title text-xl text-gray-800 mb-6">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link to="/user/tasks" className="btn btn-outline btn-lg">
              <CheckSquare className="h-6 w-6 mr-2" />
              View My Tasks
            </Link>
            <Link to="/user/swaps" className="btn btn-outline btn-lg">
              <ArrowRightLeft className="h-6 w-6 mr-2" />
              Task Swaps
            </Link>
            <Link to="/user/calendar" className="btn btn-outline btn-lg">
              <Calendar className="h-6 w-6 mr-2" />
              Calendar View
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserDashboard;
