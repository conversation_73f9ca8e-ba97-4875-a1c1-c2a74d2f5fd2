import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { tasksAPI, groupsAPI } from '../../services/api';
import { Save, X, Calendar, User, Users, AlertCircle } from 'lucide-react';
import toast from 'react-hot-toast';

const TaskForm = ({ onClose = null }) => {
  const { getPrimaryCompany, isAdmin } = useAuth();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { id: urlTaskId } = useParams();
  const primaryCompany = getPrimaryCompany();
  const taskId = urlTaskId;
  const isEditing = !!taskId;

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm({
    defaultValues: {
      title: '',
      description: '',
      priority: 'medium',
      status: 'pending',
      deadline: '',
      assigned_to: '',
      assigned_group: '',
    },
  });

  // Fetch existing task data if editing
  const { data: task } = useQuery({
    queryKey: ['task', taskId],
    queryFn: () => tasksAPI.getTask(taskId),
    enabled: !!taskId,
    onSuccess: (data) => {
      // Populate form with existing data
      Object.keys(data).forEach((key) => {
        if (key === 'deadline' && data[key]) {
          setValue(key, new Date(data[key]).toISOString().slice(0, 16));
        } else {
          setValue(key, data[key] || '');
        }
      });
    },
  });

  // Fetch groups for assignment
  const { data: groups = [] } = useQuery({
    queryKey: ['groups', primaryCompany?.company_id],
    queryFn: () => groupsAPI.getGroups(primaryCompany?.company_id),
    enabled: !!primaryCompany?.company_id,
  });

  // Create/Update task mutation
  const taskMutation = useMutation({
    mutationFn: (data) => {
      if (isEditing) {
        return tasksAPI.updateTask(taskId, data);
      } else {
        return tasksAPI.createTask(data);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['tasks']);
      toast.success(isEditing ? 'Task updated successfully!' : 'Task created successfully!');
      if (onClose) {
        onClose();
      } else {
        navigate('/tasks');
      }
    },
    onError: (error) => {
      console.error('Task mutation error:', error);
      toast.error('Failed to save task. Please try again.');
    },
  });

  const onSubmit = (data) => {
    // Prepare data for API
    const taskData = {
      ...data,
      company: primaryCompany?.company_id,
      deadline: data.deadline ? new Date(data.deadline).toISOString() : null,
      assigned_to: data.assigned_to || null,
      assigned_group: data.assigned_group || null,
    };

    // Remove empty values
    Object.keys(taskData).forEach((key) => {
      if (taskData[key] === '' || taskData[key] === undefined) {
        taskData[key] = null;
      }
    });

    taskMutation.mutate(taskData);
  };

  const handleCancel = () => {
    if (onClose) {
      onClose();
    } else {
      navigate('/tasks');
    }
  };

  // Check if user can create/edit tasks (admin only for creation)
  if (!isEditing && !isAdmin()) {
    return (
      <div className="alert alert-warning">
        <AlertCircle className="h-5 w-5" />
        <span>Only admins can create new tasks.</span>
      </div>
    );
  }

  return (
    <div className="card bg-base-100 shadow-sm max-w-2xl mx-auto">
      <div className="card-body">
        <div className="flex items-center justify-between mb-6">
          <h2 className="card-title text-2xl">
            {isEditing ? 'Edit Task' : 'Create New Task'}
          </h2>
          {onClose && (
            <button className="btn btn-ghost btn-sm" onClick={handleCancel}>
              <X className="h-5 w-5" />
            </button>
          )}
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Title */}
          <div className="form-control">
            <label className="label">
              <span className="label-text font-medium">Task Title *</span>
            </label>
            <input
              type="text"
              placeholder="Enter task title"
              className={`input input-bordered w-full ${
                errors.title ? 'input-error' : ''
              }`}
              {...register('title', {
                required: 'Task title is required',
                minLength: {
                  value: 3,
                  message: 'Title must be at least 3 characters',
                },
              })}
            />
            {errors.title && (
              <label className="label">
                <span className="label-text-alt text-error">
                  {errors.title.message}
                </span>
              </label>
            )}
          </div>

          {/* Description */}
          <div className="form-control">
            <label className="label">
              <span className="label-text font-medium">Description *</span>
            </label>
            <textarea
              placeholder="Describe the task in detail"
              className={`textarea textarea-bordered h-24 ${
                errors.description ? 'textarea-error' : ''
              }`}
              {...register('description', {
                required: 'Task description is required',
              })}
            />
            {errors.description && (
              <label className="label">
                <span className="label-text-alt text-error">
                  {errors.description.message}
                </span>
              </label>
            )}
          </div>

          {/* Priority and Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="form-control">
              <label className="label">
                <span className="label-text font-medium">Priority</span>
              </label>
              <select
                className="select select-bordered w-full"
                {...register('priority')}
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>

            {isEditing && (
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium">Status</span>
                </label>
                <select
                  className="select select-bordered w-full"
                  {...register('status')}
                >
                  <option value="pending">Pending</option>
                  <option value="in_progress">In Progress</option>
                  <option value="blocked">Blocked</option>
                  <option value="completed">Completed</option>
                </select>
              </div>
            )}
          </div>

          {/* Deadline */}
          <div className="form-control">
            <label className="label">
              <span className="label-text font-medium">Deadline</span>
            </label>
            <div className="relative">
              <input
                type="datetime-local"
                className="input input-bordered w-full pr-10"
                {...register('deadline')}
              />
              <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-base-content/50" />
            </div>
          </div>

          {/* Assignment */}
          <div className="form-control">
            <label className="label">
              <span className="label-text font-medium">Assignment</span>
            </label>
            <div className="space-y-3">
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Assign to Group</span>
                </label>
                <select
                  className="select select-bordered w-full"
                  {...register('assigned_group')}
                >
                  <option value="">Select a group (optional)</option>
                  {groups.map((group) => (
                    <option key={group.id} value={group.id}>
                      {group.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="divider">OR</div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">Assign to Specific User (User ID)</span>
                </label>
                <input
                  type="text"
                  placeholder="Enter 8-digit User ID"
                  className="input input-bordered w-full"
                  {...register('assigned_to')}
                />
                <label className="label">
                  <span className="label-text-alt">
                    Leave empty to assign to group only
                  </span>
                </label>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6">
            <button
              type="button"
              className="btn btn-ghost"
              onClick={handleCancel}
              disabled={taskMutation.isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className={`btn btn-primary ${taskMutation.isLoading ? 'loading' : ''}`}
              disabled={taskMutation.isLoading}
            >
              {taskMutation.isLoading ? (
                <span className="loading loading-spinner"></span>
              ) : (
                <>
                  <Save className="h-5 w-5 mr-2" />
                  {isEditing ? 'Update Task' : 'Create Task'}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TaskForm;
