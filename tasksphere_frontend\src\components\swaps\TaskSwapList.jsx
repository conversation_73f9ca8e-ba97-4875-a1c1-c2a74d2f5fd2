import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../context/AuthContext';
import { taskSwapsAPI } from '../../services/api';
import {
  ArrowRightLeft,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Filter,
  Eye,
} from 'lucide-react';
import toast from 'react-hot-toast';

const TaskSwapList = () => {
  const { getPrimaryCompany, isAdmin } = useAuth();
  const [statusFilter, setStatusFilter] = useState('');
  const queryClient = useQueryClient();
  const primaryCompany = getPrimaryCompany();

  const { data: swapsResponse = [], isLoading, error } = useQuery({
    queryKey: ['taskSwaps', primaryCompany?.company_id, statusFilter],
    queryFn: () => {
      const params = {
        company_id: primaryCompany?.company_id,
      };
      if (statusFilter) params.status = statusFilter;
      
      return taskSwapsAPI.getTaskSwaps(params);
    },
    enabled: !!primaryCompany?.company_id,
  });

  // Handle paginated response
  const swaps = Array.isArray(swapsResponse) ? swapsResponse : (swapsResponse?.results || []);

  // Admin approve/reject mutation
  const adminApproveMutation = useMutation({
    mutationFn: ({ swapId, action }) => taskSwapsAPI.adminApproveSwap(swapId, action),
    onSuccess: () => {
      queryClient.invalidateQueries(['taskSwaps']);
      toast.success('Swap request updated successfully!');
    },
    onError: (error) => {
      console.error('Admin approve error:', error);
      toast.error('Failed to update swap request.');
    },
  });

  // User respond mutation
  const userRespondMutation = useMutation({
    mutationFn: ({ swapId, action }) => taskSwapsAPI.userRespondSwap(swapId, action),
    onSuccess: () => {
      queryClient.invalidateQueries(['taskSwaps']);
      queryClient.invalidateQueries(['tasks']); // Refresh tasks as assignment might change
      toast.success('Swap response submitted successfully!');
    },
    onError: (error) => {
      console.error('User respond error:', error);
      toast.error('Failed to respond to swap request.');
    },
  });

  const getStatusBadge = (status) => {
    const statusMap = {
      pending_admin: 'badge-warning',
      pending_user: 'badge-info',
      approved: 'badge-success',
      rejected: 'badge-error',
    };
    return statusMap[status] || 'badge-ghost';
  };

  const getStatusIcon = (status) => {
    const iconMap = {
      pending_admin: Clock,
      pending_user: AlertCircle,
      approved: CheckCircle,
      rejected: XCircle,
    };
    return iconMap[status] || ArrowRightLeft;
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const handleAdminAction = (swapId, action) => {
    adminApproveMutation.mutate({ swapId, action });
  };

  const handleUserResponse = (swapId, action) => {
    userRespondMutation.mutate({ swapId, action });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-error">
        <AlertCircle className="h-5 w-5" />
        <span>Failed to load task swaps. Please try again.</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Task Swaps</h1>
          <p className="text-base-content/70 mt-1">
            Manage task swap requests and approvals
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="card bg-base-100 shadow-sm">
        <div className="card-body p-4">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              <span className="font-medium">Filters:</span>
            </div>
            
            <select
              className="select select-bordered select-sm"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="">All Status</option>
              <option value="pending_admin">Pending Admin</option>
              <option value="pending_user">Pending User</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>

            <div className="ml-auto">
              <span className="text-sm text-base-content/70">
                {swaps.length} swap{swaps.length !== 1 ? 's' : ''}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Swaps List */}
      {swaps.length === 0 ? (
        <div className="text-center py-12">
          <ArrowRightLeft className="h-16 w-16 mx-auto mb-4 text-base-content/30" />
          <h3 className="text-lg font-medium mb-2">No task swaps found</h3>
          <p className="text-base-content/70">
            {statusFilter
              ? 'Try adjusting your filters'
              : 'Task swap requests will appear here'}
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {swaps.map((swap) => {
            const StatusIcon = getStatusIcon(swap.status);
            
            return (
              <div key={swap.id} className="card bg-base-100 shadow-sm hover:shadow-md transition-shadow">
                <div className="card-body">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3">
                        <StatusIcon className="h-5 w-5 text-primary" />
                        <h3 className="font-semibold text-lg">{swap.task_title}</h3>
                        <span className={`badge ${getStatusBadge(swap.status)}`}>
                          {swap.status.replace('_', ' ')}
                        </span>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <p className="text-sm text-base-content/70">Requested by:</p>
                          <p className="font-medium">{swap.requested_by_name}</p>
                        </div>
                        <div>
                          <p className="text-sm text-base-content/70">Requested to:</p>
                          <p className="font-medium">{swap.requested_to_name}</p>
                        </div>
                        <div>
                          <p className="text-sm text-base-content/70">Created:</p>
                          <p className="font-medium">{formatDate(swap.created_at)}</p>
                        </div>
                        {swap.admin_approved_by_name && (
                          <div>
                            <p className="text-sm text-base-content/70">Admin approved by:</p>
                            <p className="font-medium">{swap.admin_approved_by_name}</p>
                          </div>
                        )}
                      </div>

                      {swap.reason && (
                        <div className="mb-4">
                          <p className="text-sm text-base-content/70">Reason:</p>
                          <p className="text-sm bg-base-200 p-3 rounded-lg">{swap.reason}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex justify-end gap-2 mt-4">
                    {/* Admin Actions */}
                    {isAdmin() && swap.status === 'pending_admin' && (
                      <>
                        <button
                          className="btn btn-error btn-sm"
                          onClick={() => handleAdminAction(swap.id, 'reject')}
                          disabled={adminApproveMutation.isLoading}
                        >
                          <XCircle className="h-4 w-4 mr-1" />
                          Reject
                        </button>
                        <button
                          className="btn btn-success btn-sm"
                          onClick={() => handleAdminAction(swap.id, 'approve')}
                          disabled={adminApproveMutation.isLoading}
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Approve
                        </button>
                      </>
                    )}

                    {/* User Actions */}
                    {swap.status === 'pending_user' && (
                      <>
                        <button
                          className="btn btn-error btn-sm"
                          onClick={() => handleUserResponse(swap.id, 'reject')}
                          disabled={userRespondMutation.isLoading}
                        >
                          <XCircle className="h-4 w-4 mr-1" />
                          Reject
                        </button>
                        <button
                          className="btn btn-success btn-sm"
                          onClick={() => handleUserResponse(swap.id, 'accept')}
                          disabled={userRespondMutation.isLoading}
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Accept
                        </button>
                      </>
                    )}

                    {/* View Details */}
                    <button className="btn btn-ghost btn-sm">
                      <Eye className="h-4 w-4 mr-1" />
                      Details
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default TaskSwapList;
