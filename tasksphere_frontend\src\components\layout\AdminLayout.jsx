import { useState } from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import {
  LayoutDashboard,
  CheckSquare,
  Users,
  ArrowRightLeft,
  Bell,
  User,
  LogOut,
  Menu,
  Settings,
  BarChart3,
  Building2,
} from 'lucide-react';

const AdminLayout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
    navigate('/organization/login');
  };

  const navigation = [
    { name: 'Dashboard', href: '/admin/dashboard', icon: LayoutDashboard },
    { name: 'Tasks', href: '/admin/tasks', icon: CheckSquare },
    { name: 'Groups', href: '/admin/groups', icon: Users },
    { name: 'Task Swaps', href: '/admin/swaps', icon: ArrowRightLeft },
    { name: 'Analytics', href: '/admin/analytics', icon: BarChart3 },
    { name: 'Settings', href: '/admin/settings', icon: Settings },
    { name: 'Notifications', href: '/admin/notifications', icon: Bell },
  ];

  const isActive = (href) => location.pathname === href;

  return (
    <div className="drawer lg:drawer-open">
      <input id="admin-drawer-toggle" type="checkbox" className="drawer-toggle" checked={sidebarOpen} onChange={() => setSidebarOpen(!sidebarOpen)} />
      
      {/* Main content */}
      <div className="drawer-content flex flex-col">
        {/* Top bar */}
        <div className="navbar bg-blue-600 text-white shadow-lg lg:hidden">
          <div className="flex-none">
            <label htmlFor="admin-drawer-toggle" className="btn btn-square btn-ghost text-white">
              <Menu className="h-5 w-5" />
            </label>
          </div>
          <div className="flex-1">
            <h1 className="text-xl font-bold">TaskSphere Admin</h1>
          </div>
          <div className="flex-none">
            <Link to="/admin/notifications" className="btn btn-ghost btn-sm text-white">
              <Bell className="h-5 w-5" />
            </Link>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1 p-6 bg-gray-50 min-h-screen lg:pt-20 lg:pl-64">
          <Outlet />
        </main>
      </div>

      {/* Sidebar */}
      <div className="drawer-side">
        <label htmlFor="admin-drawer-toggle" className="drawer-overlay"></label>
        <aside className="w-64 min-h-full bg-blue-800 text-white shadow-lg">
          {/* Sidebar Header */}
          <div className="flex items-center justify-center h-16 px-4 border-b border-blue-700 bg-blue-900">
            <Building2 className="h-6 w-6 mr-2" />
            <h1 className="text-xl font-bold">Admin Portal</h1>
          </div>

          {/* Navigation */}
          <nav className="mt-8">
            <div className="px-4 space-y-2">
              {navigation.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                      isActive(item.href)
                        ? 'bg-blue-600 text-white'
                        : 'text-blue-100 hover:bg-blue-700 hover:text-white'
                    }`}
                    onClick={() => setSidebarOpen(false)}
                  >
                    <Icon className="h-5 w-5 mr-3" />
                    {item.name}
                  </Link>
                );
              })}
            </div>
          </nav>

          {/* User info at bottom */}
          <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-blue-700">
            <div className="flex items-center space-x-3 mb-3">
              <div className="avatar placeholder">
                <div className="bg-blue-600 text-white rounded-full w-10">
                  <span className="text-sm">
                    {user?.first_name?.[0]}{user?.last_name?.[0]}
                  </span>
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  {user?.first_name} {user?.last_name}
                </p>
                <p className="text-xs text-blue-200 truncate">
                  Admin • ID: {user?.user_id}
                </p>
              </div>
            </div>
            
            <div className="flex space-x-2">
              <Link
                to="/admin/profile"
                className="btn btn-ghost btn-sm flex-1 text-blue-100 hover:text-white"
                onClick={() => setSidebarOpen(false)}
              >
                <User className="h-4 w-4 mr-1" />
                Profile
              </Link>
              <button
                onClick={handleLogout}
                className="btn btn-ghost btn-sm flex-1 text-blue-100 hover:text-white"
              >
                <LogOut className="h-4 w-4 mr-1" />
                Logout
              </button>
            </div>
          </div>
        </aside>
      </div>

      {/* Desktop top bar */}
      <div className="hidden lg:block fixed top-0 left-64 right-0 z-10 bg-white shadow-sm border-b border-gray-200">
        <div className="flex items-center justify-between h-16 px-6">
          <div className="flex-1">
            <h2 className="text-lg font-semibold text-gray-800">Organization Management</h2>
          </div>
          <div className="flex items-center space-x-4">
            {/* Company selector */}
            {user?.companies && user.companies.length > 0 && (
              <div className="dropdown dropdown-end">
                <label tabIndex={0} className="btn btn-ghost btn-sm">
                  <Building2 className="h-4 w-4 mr-2" />
                  {user.companies[0].company_name}
                </label>
                <ul
                  tabIndex={0}
                  className="dropdown-content menu p-2 shadow bg-white rounded-box w-52"
                >
                  {user.companies.map((company) => (
                    <li key={company.company_id}>
                      <a>
                        <div>
                          <div className="font-medium">{company.company_name}</div>
                          <div className="text-xs opacity-70">{company.role}</div>
                        </div>
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Notifications */}
            <Link to="/admin/notifications" className="btn btn-ghost btn-sm">
              <Bell className="h-5 w-5" />
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminLayout;
