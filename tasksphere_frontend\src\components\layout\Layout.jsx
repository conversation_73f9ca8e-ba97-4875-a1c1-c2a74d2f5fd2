import React, { useState } from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import {
  Home,
  CheckSquare,
  Users,
  ArrowRightLeft,
  Bell,
  User,
  LogOut,
  Menu,
  X,
} from 'lucide-react';

const Layout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, logout, isAdmin } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: Home },
    { name: 'Tasks', href: '/tasks', icon: CheckSquare },
    { name: 'Task Swaps', href: '/swaps', icon: ArrowRightLeft },
    ...(isAdmin() ? [{ name: 'Groups', href: '/groups', icon: Users }] : []),
    { name: 'Notifications', href: '/notifications', icon: Bell },
  ];

  const isActive = (href) => location.pathname === href;

  return (
    <div className="min-h-screen bg-base-200">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <div className="fixed inset-0 bg-black opacity-50"></div>
        </div>
      )}

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-base-100 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex items-center justify-between h-16 px-4 border-b border-base-300">
          <h1 className="text-xl font-bold text-primary">TaskSphere</h1>
          <button
            className="lg:hidden btn btn-ghost btn-sm"
            onClick={() => setSidebarOpen(false)}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <nav className="mt-8">
          <div className="px-4 space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                    isActive(item.href)
                      ? 'bg-primary text-primary-content'
                      : 'text-base-content hover:bg-base-200'
                  }`}
                  onClick={() => setSidebarOpen(false)}
                >
                  <Icon className="h-5 w-5 mr-3" />
                  {item.name}
                </Link>
              );
            })}
          </div>
        </nav>

        {/* User info at bottom */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-base-300">
          <div className="flex items-center space-x-3 mb-3">
            <div className="avatar placeholder">
              <div className="bg-primary text-primary-content rounded-full w-10">
                <span className="text-sm">
                  {user?.first_name?.[0]}{user?.last_name?.[0]}
                </span>
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-base-content truncate">
                {user?.first_name} {user?.last_name}
              </p>
              <p className="text-xs text-base-content/70 truncate">
                ID: {user?.user_id}
              </p>
            </div>
          </div>
          
          <div className="flex space-x-2">
            <Link
              to="/profile"
              className="btn btn-ghost btn-sm flex-1"
              onClick={() => setSidebarOpen(false)}
            >
              <User className="h-4 w-4 mr-1" />
              Profile
            </Link>
            <button
              onClick={handleLogout}
              className="btn btn-ghost btn-sm flex-1"
            >
              <LogOut className="h-4 w-4 mr-1" />
              Logout
            </button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-10 bg-base-100 shadow-sm border-b border-base-300">
          <div className="flex items-center justify-between h-16 px-4">
            <button
              className="lg:hidden btn btn-ghost btn-sm"
              onClick={() => setSidebarOpen(true)}
            >
              <Menu className="h-5 w-5" />
            </button>

            <div className="flex items-center space-x-4">
              {/* Company selector */}
              {user?.companies && user.companies.length > 0 && (
                <div className="dropdown dropdown-end">
                  <label tabIndex={0} className="btn btn-ghost btn-sm">
                    {user.companies[0].company_name}
                  </label>
                  <ul
                    tabIndex={0}
                    className="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-52"
                  >
                    {user.companies.map((company) => (
                      <li key={company.company_id}>
                        <a>
                          <div>
                            <div className="font-medium">{company.company_name}</div>
                            <div className="text-xs opacity-70">{company.role}</div>
                          </div>
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Notifications */}
              <Link to="/notifications" className="btn btn-ghost btn-sm">
                <Bell className="h-5 w-5" />
              </Link>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="p-6">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default Layout;
