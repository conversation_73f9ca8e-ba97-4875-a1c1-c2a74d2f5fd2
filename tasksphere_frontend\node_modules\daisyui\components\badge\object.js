export default {".badge":{"display":"inline-flex","align-items":"center","justify-content":"center","gap":"calc(0.25rem * 2)","border-radius":"var(--radius-selector)","vertical-align":"middle","color":"var(--badge-fg)","border":"var(--border) solid var(--badge-color, var(--color-base-200))","font-size":"0.875rem","width":"fit-content","padding-inline":"calc(0.25rem * 3 - var(--border))","background-size":"auto, calc(var(--noise) * 100%)","background-image":"none, var(--fx-noise)","background-color":"var(--badge-bg)","--badge-bg":"var(--badge-color, var(--color-base-100))","--badge-fg":"var(--color-base-content)","--size":"calc(var(--size-selector, 0.25rem) * 6)","height":"var(--size)"},".badge-outline":{"color":"var(--badge-color)","--badge-bg":"#0000","background-image":"none","border-color":"currentColor"},".badge-dash":{"color":"var(--badge-color)","--badge-bg":"#0000","background-image":"none","border-color":"currentColor","border-style":"dashed"},".badge-soft":{"color":"var(--badge-color, var(--color-base-content))","background-color":"color-mix( in oklab, var(--badge-color, var(--color-base-content)) 8%, var(--color-base-100) )","border-color":"color-mix( in oklab, var(--badge-color, var(--color-base-content)) 10%, var(--color-base-100) )","background-image":"none"},".badge-primary":{"--badge-color":"var(--color-primary)","--badge-fg":"var(--color-primary-content)"},".badge-secondary":{"--badge-color":"var(--color-secondary)","--badge-fg":"var(--color-secondary-content)"},".badge-accent":{"--badge-color":"var(--color-accent)","--badge-fg":"var(--color-accent-content)"},".badge-neutral":{"--badge-color":"var(--color-neutral)","--badge-fg":"var(--color-neutral-content)"},".badge-info":{"--badge-color":"var(--color-info)","--badge-fg":"var(--color-info-content)"},".badge-success":{"--badge-color":"var(--color-success)","--badge-fg":"var(--color-success-content)"},".badge-warning":{"--badge-color":"var(--color-warning)","--badge-fg":"var(--color-warning-content)"},".badge-error":{"--badge-color":"var(--color-error)","--badge-fg":"var(--color-error-content)"},".badge-ghost":{"border-color":"var(--color-base-200)","background-color":"var(--color-base-200)","color":"var(--color-base-content)","background-image":"none"},".badge-xs":{"--size":"calc(var(--size-selector, 0.25rem) * 4)","font-size":"0.625rem","padding-inline":"calc(0.25rem * 2 - var(--border))"},".badge-sm":{"--size":"calc(var(--size-selector, 0.25rem) * 5)","font-size":"0.75rem","padding-inline":"calc(0.25rem * 2.5 - var(--border))"},".badge-md":{"--size":"calc(var(--size-selector, 0.25rem) * 6)","font-size":"0.875rem","padding-inline":"calc(0.25rem * 3 - var(--border))"},".badge-lg":{"--size":"calc(var(--size-selector, 0.25rem) * 7)","font-size":"1rem","padding-inline":"calc(0.25rem * 3.5 - var(--border))"},".badge-xl":{"--size":"calc(var(--size-selector, 0.25rem) * 8)","font-size":"1.125rem","padding-inline":"calc(0.25rem * 4 - var(--border))"}};