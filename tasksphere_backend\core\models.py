from django.db import models
from django.conf import settings
import string
import random


def generate_company_code():
    """Generate a unique 6-digit company code"""
    return ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))


class Company(models.Model):
    """Company model for multi-tenant system"""
    name = models.CharField(max_length=100)
    company_code = models.CharField(
        max_length=6,
        unique=True,
        default=generate_company_code,
        help_text="6-digit company code for users to join"
    )
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    def save(self, *args, **kwargs):
        # Ensure company_code is unique
        while Company.objects.filter(company_code=self.company_code).exists():
            self.company_code = generate_company_code()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} ({self.company_code})"

    class Meta:
        verbose_name_plural = "Companies"


class CompanyMembership(models.Model):
    """Relationship between User and Company with role"""
    ROLE_CHOICES = [
        ('user', 'User'),
        ('admin', 'Admin'),
    ]

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    role = models.CharField(max_length=10, choices=ROLE_CHOICES, default='user')
    joined_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ['user', 'company']
        verbose_name = "Company Membership"

    def __str__(self):
        return f"{self.user.user_id} - {self.company.name} ({self.role})"


class Group(models.Model):
    """Groups within a company for organizing users"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='groups')
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_groups'
    )
    members = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        through='GroupMembership',
        through_fields=('group', 'user'),
        related_name='user_groups'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ['name', 'company']

    def __str__(self):
        return f"{self.name} - {self.company.name}"


class GroupMembership(models.Model):
    """Membership relationship between User and Group"""
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    group = models.ForeignKey(Group, on_delete=models.CASCADE)
    added_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='added_group_members'
    )
    joined_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ['user', 'group']

    def __str__(self):
        return f"{self.user.user_id} in {self.group.name}"


class Task(models.Model):
    """Task model for task management"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('blocked', 'Blocked'),
        ('completed', 'Completed'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    title = models.CharField(max_length=200)
    description = models.TextField()
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='tasks')
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_tasks'
    )
    assigned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='assigned_tasks',
        null=True,
        blank=True
    )
    assigned_group = models.ForeignKey(
        Group,
        on_delete=models.CASCADE,
        related_name='assigned_tasks',
        null=True,
        blank=True
    )
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')
    deadline = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.title} - {self.company.name}"

    class Meta:
        ordering = ['-created_at']


class TaskSwap(models.Model):
    """Task swap requests between users"""
    STATUS_CHOICES = [
        ('pending_admin', 'Pending Admin Approval'),
        ('pending_user', 'Pending User Approval'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]

    task = models.ForeignKey(Task, on_delete=models.CASCADE, related_name='swap_requests')
    requested_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='initiated_swaps'
    )
    requested_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='received_swaps'
    )
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending_admin')
    admin_approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='admin_approved_swaps',
        null=True,
        blank=True
    )
    admin_approved_at = models.DateTimeField(null=True, blank=True)
    user_response = models.BooleanField(null=True, blank=True)  # True=Accept, False=Reject
    user_responded_at = models.DateTimeField(null=True, blank=True)
    reason = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Swap: {self.task.title} ({self.requested_by.user_id} -> {self.requested_to.user_id})"

    class Meta:
        ordering = ['-created_at']
