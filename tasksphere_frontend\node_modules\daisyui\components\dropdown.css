/*! 🌼 daisyUI 5.0.45 - MIT License */ @layer utilities{.dropdown{position-area:var(--anchor-v,bottom)var(--anchor-h,span-right);display:inline-block;position:relative;&>:not(summary):focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}& .dropdown-content{position:absolute}&:not(details,.dropdown-open,.dropdown-hover:hover,:focus-within){& .dropdown-content{transform-origin:top;opacity:0;display:none;scale:95%}}&[popover],& .dropdown-content{z-index:999;transition-behavior:allow-discrete;transition-property:opacity,scale,display;transition-duration:.2s;transition-timing-function:cubic-bezier(.4,0,.2,1);animation:.2s dropdown}@starting-style{&[popover],& .dropdown-content{opacity:0;scale:95%}}&.dropdown-open,&:not(.dropdown-hover):focus,&:focus-within{&>[tabindex]:first-child{pointer-events:none}& .dropdown-content{opacity:1}}&.dropdown-hover:hover{& .dropdown-content{opacity:1;scale:100%}}&:is(details){& summary{&::-webkit-details-marker{display:none}}}&.dropdown-open,&:focus,&:focus-within{& .dropdown-content{scale:100%}}&:where([popover]){background:0 0}&[popover]{color:inherit;position:fixed;@supports not (position-area:bottom){&{margin:auto}&.dropdown-open:not(:popover-open){transform-origin:top;opacity:0;display:none;scale:95%}&::backdrop{background-color:oklab(0% none none/.3)}}&:not(.dropdown-open,:popover-open){transform-origin:top;opacity:0;display:none;scale:95%}}}.dropdown-start{--anchor-h:span-right;& :where(.dropdown-content){inset-inline-end:auto;translate:0;[dir=rtl] &{translate:0}}&.dropdown-left{--anchor-h:left;--anchor-v:span-bottom;& .dropdown-content{top:0;bottom:auto}}&.dropdown-right{--anchor-h:right;--anchor-v:span-bottom;& .dropdown-content{top:0;bottom:auto}}}.dropdown-center{--anchor-h:center;& :where(.dropdown-content){inset-inline-end:50%;translate:50%;[dir=rtl] &{translate:-50%}}&.dropdown-left{--anchor-h:left;--anchor-v:center;& .dropdown-content{top:auto;bottom:50%;translate:0 50%}}&.dropdown-right{--anchor-h:right;--anchor-v:center;& .dropdown-content{top:auto;bottom:50%;translate:0 50%}}}.dropdown-end{--anchor-h:span-left;& :where(.dropdown-content){inset-inline-end:0;translate:0;[dir=rtl] &{translate:0}}&.dropdown-left{--anchor-h:left;--anchor-v:span-top;& .dropdown-content{top:auto;bottom:0}}&.dropdown-right{--anchor-h:right;--anchor-v:span-top;& .dropdown-content{top:auto;bottom:0}}}.dropdown-left{--anchor-h:left;--anchor-v:span-bottom;& .dropdown-content{inset-inline-end:100%;transform-origin:100%;top:0;bottom:auto}}.dropdown-right{--anchor-h:right;--anchor-v:span-bottom;& .dropdown-content{inset-inline-start:100%;transform-origin:0;top:0;bottom:auto}}.dropdown-bottom{--anchor-v:bottom;& .dropdown-content{transform-origin:top;top:100%;bottom:auto}}.dropdown-top{--anchor-v:top;& .dropdown-content{transform-origin:bottom;top:auto;bottom:100%}}@keyframes dropdown{0%{opacity:0}}@media (width>=640px){.sm\:dropdown{position-area:var(--anchor-v,bottom)var(--anchor-h,span-right);display:inline-block;position:relative;&>:not(summary):focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}& .dropdown-content{position:absolute}&:not(details,.dropdown-open,.dropdown-hover:hover,:focus-within){& .dropdown-content{transform-origin:top;opacity:0;display:none;scale:95%}}&[popover],& .dropdown-content{z-index:999;transition-behavior:allow-discrete;transition-property:opacity,scale,display;transition-duration:.2s;transition-timing-function:cubic-bezier(.4,0,.2,1);animation:.2s dropdown}@starting-style{&[popover],& .dropdown-content{opacity:0;scale:95%}}&.dropdown-open,&:not(.dropdown-hover):focus,&:focus-within{&>[tabindex]:first-child{pointer-events:none}& .dropdown-content{opacity:1}}&.dropdown-hover:hover{& .dropdown-content{opacity:1;scale:100%}}&:is(details){& summary{&::-webkit-details-marker{display:none}}}&.dropdown-open,&:focus,&:focus-within{& .dropdown-content{scale:100%}}&:where([popover]){background:0 0}&[popover]{color:inherit;position:fixed;@supports not (position-area:bottom){&{margin:auto}&.dropdown-open:not(:popover-open){transform-origin:top;opacity:0;display:none;scale:95%}&::backdrop{background-color:oklab(0% none none/.3)}}&:not(.dropdown-open,:popover-open){transform-origin:top;opacity:0;display:none;scale:95%}}}.sm\:dropdown-start{--anchor-h:span-right;& :where(.dropdown-content){inset-inline-end:auto;translate:0;[dir=rtl] &{translate:0}}&.dropdown-left{--anchor-h:left;--anchor-v:span-bottom;& .dropdown-content{top:0;bottom:auto}}&.dropdown-right{--anchor-h:right;--anchor-v:span-bottom;& .dropdown-content{top:0;bottom:auto}}}.sm\:dropdown-center{--anchor-h:center;& :where(.dropdown-content){inset-inline-end:50%;translate:50%;[dir=rtl] &{translate:-50%}}&.dropdown-left{--anchor-h:left;--anchor-v:center;& .dropdown-content{top:auto;bottom:50%;translate:0 50%}}&.dropdown-right{--anchor-h:right;--anchor-v:center;& .dropdown-content{top:auto;bottom:50%;translate:0 50%}}}.sm\:dropdown-end{--anchor-h:span-left;& :where(.dropdown-content){inset-inline-end:0;translate:0;[dir=rtl] &{translate:0}}&.dropdown-left{--anchor-h:left;--anchor-v:span-top;& .dropdown-content{top:auto;bottom:0}}&.dropdown-right{--anchor-h:right;--anchor-v:span-top;& .dropdown-content{top:auto;bottom:0}}}.sm\:dropdown-left{--anchor-h:left;--anchor-v:span-bottom;& .dropdown-content{inset-inline-end:100%;transform-origin:100%;top:0;bottom:auto}}.sm\:dropdown-right{--anchor-h:right;--anchor-v:span-bottom;& .dropdown-content{inset-inline-start:100%;transform-origin:0;top:0;bottom:auto}}.sm\:dropdown-bottom{--anchor-v:bottom;& .dropdown-content{transform-origin:top;top:100%;bottom:auto}}.sm\:dropdown-top{--anchor-v:top;& .dropdown-content{transform-origin:bottom;top:auto;bottom:100%}}}@media (width>=768px){.md\:dropdown{position-area:var(--anchor-v,bottom)var(--anchor-h,span-right);display:inline-block;position:relative;&>:not(summary):focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}& .dropdown-content{position:absolute}&:not(details,.dropdown-open,.dropdown-hover:hover,:focus-within){& .dropdown-content{transform-origin:top;opacity:0;display:none;scale:95%}}&[popover],& .dropdown-content{z-index:999;transition-behavior:allow-discrete;transition-property:opacity,scale,display;transition-duration:.2s;transition-timing-function:cubic-bezier(.4,0,.2,1);animation:.2s dropdown}@starting-style{&[popover],& .dropdown-content{opacity:0;scale:95%}}&.dropdown-open,&:not(.dropdown-hover):focus,&:focus-within{&>[tabindex]:first-child{pointer-events:none}& .dropdown-content{opacity:1}}&.dropdown-hover:hover{& .dropdown-content{opacity:1;scale:100%}}&:is(details){& summary{&::-webkit-details-marker{display:none}}}&.dropdown-open,&:focus,&:focus-within{& .dropdown-content{scale:100%}}&:where([popover]){background:0 0}&[popover]{color:inherit;position:fixed;@supports not (position-area:bottom){&{margin:auto}&.dropdown-open:not(:popover-open){transform-origin:top;opacity:0;display:none;scale:95%}&::backdrop{background-color:oklab(0% none none/.3)}}&:not(.dropdown-open,:popover-open){transform-origin:top;opacity:0;display:none;scale:95%}}}.md\:dropdown-start{--anchor-h:span-right;& :where(.dropdown-content){inset-inline-end:auto;translate:0;[dir=rtl] &{translate:0}}&.dropdown-left{--anchor-h:left;--anchor-v:span-bottom;& .dropdown-content{top:0;bottom:auto}}&.dropdown-right{--anchor-h:right;--anchor-v:span-bottom;& .dropdown-content{top:0;bottom:auto}}}.md\:dropdown-center{--anchor-h:center;& :where(.dropdown-content){inset-inline-end:50%;translate:50%;[dir=rtl] &{translate:-50%}}&.dropdown-left{--anchor-h:left;--anchor-v:center;& .dropdown-content{top:auto;bottom:50%;translate:0 50%}}&.dropdown-right{--anchor-h:right;--anchor-v:center;& .dropdown-content{top:auto;bottom:50%;translate:0 50%}}}.md\:dropdown-end{--anchor-h:span-left;& :where(.dropdown-content){inset-inline-end:0;translate:0;[dir=rtl] &{translate:0}}&.dropdown-left{--anchor-h:left;--anchor-v:span-top;& .dropdown-content{top:auto;bottom:0}}&.dropdown-right{--anchor-h:right;--anchor-v:span-top;& .dropdown-content{top:auto;bottom:0}}}.md\:dropdown-left{--anchor-h:left;--anchor-v:span-bottom;& .dropdown-content{inset-inline-end:100%;transform-origin:100%;top:0;bottom:auto}}.md\:dropdown-right{--anchor-h:right;--anchor-v:span-bottom;& .dropdown-content{inset-inline-start:100%;transform-origin:0;top:0;bottom:auto}}.md\:dropdown-bottom{--anchor-v:bottom;& .dropdown-content{transform-origin:top;top:100%;bottom:auto}}.md\:dropdown-top{--anchor-v:top;& .dropdown-content{transform-origin:bottom;top:auto;bottom:100%}}}@media (width>=1024px){.lg\:dropdown{position-area:var(--anchor-v,bottom)var(--anchor-h,span-right);display:inline-block;position:relative;&>:not(summary):focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}& .dropdown-content{position:absolute}&:not(details,.dropdown-open,.dropdown-hover:hover,:focus-within){& .dropdown-content{transform-origin:top;opacity:0;display:none;scale:95%}}&[popover],& .dropdown-content{z-index:999;transition-behavior:allow-discrete;transition-property:opacity,scale,display;transition-duration:.2s;transition-timing-function:cubic-bezier(.4,0,.2,1);animation:.2s dropdown}@starting-style{&[popover],& .dropdown-content{opacity:0;scale:95%}}&.dropdown-open,&:not(.dropdown-hover):focus,&:focus-within{&>[tabindex]:first-child{pointer-events:none}& .dropdown-content{opacity:1}}&.dropdown-hover:hover{& .dropdown-content{opacity:1;scale:100%}}&:is(details){& summary{&::-webkit-details-marker{display:none}}}&.dropdown-open,&:focus,&:focus-within{& .dropdown-content{scale:100%}}&:where([popover]){background:0 0}&[popover]{color:inherit;position:fixed;@supports not (position-area:bottom){&{margin:auto}&.dropdown-open:not(:popover-open){transform-origin:top;opacity:0;display:none;scale:95%}&::backdrop{background-color:oklab(0% none none/.3)}}&:not(.dropdown-open,:popover-open){transform-origin:top;opacity:0;display:none;scale:95%}}}.lg\:dropdown-start{--anchor-h:span-right;& :where(.dropdown-content){inset-inline-end:auto;translate:0;[dir=rtl] &{translate:0}}&.dropdown-left{--anchor-h:left;--anchor-v:span-bottom;& .dropdown-content{top:0;bottom:auto}}&.dropdown-right{--anchor-h:right;--anchor-v:span-bottom;& .dropdown-content{top:0;bottom:auto}}}.lg\:dropdown-center{--anchor-h:center;& :where(.dropdown-content){inset-inline-end:50%;translate:50%;[dir=rtl] &{translate:-50%}}&.dropdown-left{--anchor-h:left;--anchor-v:center;& .dropdown-content{top:auto;bottom:50%;translate:0 50%}}&.dropdown-right{--anchor-h:right;--anchor-v:center;& .dropdown-content{top:auto;bottom:50%;translate:0 50%}}}.lg\:dropdown-end{--anchor-h:span-left;& :where(.dropdown-content){inset-inline-end:0;translate:0;[dir=rtl] &{translate:0}}&.dropdown-left{--anchor-h:left;--anchor-v:span-top;& .dropdown-content{top:auto;bottom:0}}&.dropdown-right{--anchor-h:right;--anchor-v:span-top;& .dropdown-content{top:auto;bottom:0}}}.lg\:dropdown-left{--anchor-h:left;--anchor-v:span-bottom;& .dropdown-content{inset-inline-end:100%;transform-origin:100%;top:0;bottom:auto}}.lg\:dropdown-right{--anchor-h:right;--anchor-v:span-bottom;& .dropdown-content{inset-inline-start:100%;transform-origin:0;top:0;bottom:auto}}.lg\:dropdown-bottom{--anchor-v:bottom;& .dropdown-content{transform-origin:top;top:100%;bottom:auto}}.lg\:dropdown-top{--anchor-v:top;& .dropdown-content{transform-origin:bottom;top:auto;bottom:100%}}}@media (width>=1280px){.xl\:dropdown{position-area:var(--anchor-v,bottom)var(--anchor-h,span-right);display:inline-block;position:relative;&>:not(summary):focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}& .dropdown-content{position:absolute}&:not(details,.dropdown-open,.dropdown-hover:hover,:focus-within){& .dropdown-content{transform-origin:top;opacity:0;display:none;scale:95%}}&[popover],& .dropdown-content{z-index:999;transition-behavior:allow-discrete;transition-property:opacity,scale,display;transition-duration:.2s;transition-timing-function:cubic-bezier(.4,0,.2,1);animation:.2s dropdown}@starting-style{&[popover],& .dropdown-content{opacity:0;scale:95%}}&.dropdown-open,&:not(.dropdown-hover):focus,&:focus-within{&>[tabindex]:first-child{pointer-events:none}& .dropdown-content{opacity:1}}&.dropdown-hover:hover{& .dropdown-content{opacity:1;scale:100%}}&:is(details){& summary{&::-webkit-details-marker{display:none}}}&.dropdown-open,&:focus,&:focus-within{& .dropdown-content{scale:100%}}&:where([popover]){background:0 0}&[popover]{color:inherit;position:fixed;@supports not (position-area:bottom){&{margin:auto}&.dropdown-open:not(:popover-open){transform-origin:top;opacity:0;display:none;scale:95%}&::backdrop{background-color:oklab(0% none none/.3)}}&:not(.dropdown-open,:popover-open){transform-origin:top;opacity:0;display:none;scale:95%}}}.xl\:dropdown-start{--anchor-h:span-right;& :where(.dropdown-content){inset-inline-end:auto;translate:0;[dir=rtl] &{translate:0}}&.dropdown-left{--anchor-h:left;--anchor-v:span-bottom;& .dropdown-content{top:0;bottom:auto}}&.dropdown-right{--anchor-h:right;--anchor-v:span-bottom;& .dropdown-content{top:0;bottom:auto}}}.xl\:dropdown-center{--anchor-h:center;& :where(.dropdown-content){inset-inline-end:50%;translate:50%;[dir=rtl] &{translate:-50%}}&.dropdown-left{--anchor-h:left;--anchor-v:center;& .dropdown-content{top:auto;bottom:50%;translate:0 50%}}&.dropdown-right{--anchor-h:right;--anchor-v:center;& .dropdown-content{top:auto;bottom:50%;translate:0 50%}}}.xl\:dropdown-end{--anchor-h:span-left;& :where(.dropdown-content){inset-inline-end:0;translate:0;[dir=rtl] &{translate:0}}&.dropdown-left{--anchor-h:left;--anchor-v:span-top;& .dropdown-content{top:auto;bottom:0}}&.dropdown-right{--anchor-h:right;--anchor-v:span-top;& .dropdown-content{top:auto;bottom:0}}}.xl\:dropdown-left{--anchor-h:left;--anchor-v:span-bottom;& .dropdown-content{inset-inline-end:100%;transform-origin:100%;top:0;bottom:auto}}.xl\:dropdown-right{--anchor-h:right;--anchor-v:span-bottom;& .dropdown-content{inset-inline-start:100%;transform-origin:0;top:0;bottom:auto}}.xl\:dropdown-bottom{--anchor-v:bottom;& .dropdown-content{transform-origin:top;top:100%;bottom:auto}}.xl\:dropdown-top{--anchor-v:top;& .dropdown-content{transform-origin:bottom;top:auto;bottom:100%}}}@media (width>=1536px){.\32 xl\:dropdown{position-area:var(--anchor-v,bottom)var(--anchor-h,span-right);display:inline-block;position:relative;&>:not(summary):focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}& .dropdown-content{position:absolute}&:not(details,.dropdown-open,.dropdown-hover:hover,:focus-within){& .dropdown-content{transform-origin:top;opacity:0;display:none;scale:95%}}&[popover],& .dropdown-content{z-index:999;transition-behavior:allow-discrete;transition-property:opacity,scale,display;transition-duration:.2s;transition-timing-function:cubic-bezier(.4,0,.2,1);animation:.2s dropdown}@starting-style{&[popover],& .dropdown-content{opacity:0;scale:95%}}&.dropdown-open,&:not(.dropdown-hover):focus,&:focus-within{&>[tabindex]:first-child{pointer-events:none}& .dropdown-content{opacity:1}}&.dropdown-hover:hover{& .dropdown-content{opacity:1;scale:100%}}&:is(details){& summary{&::-webkit-details-marker{display:none}}}&.dropdown-open,&:focus,&:focus-within{& .dropdown-content{scale:100%}}&:where([popover]){background:0 0}&[popover]{color:inherit;position:fixed;@supports not (position-area:bottom){&{margin:auto}&.dropdown-open:not(:popover-open){transform-origin:top;opacity:0;display:none;scale:95%}&::backdrop{background-color:oklab(0% none none/.3)}}&:not(.dropdown-open,:popover-open){transform-origin:top;opacity:0;display:none;scale:95%}}}.\32 xl\:dropdown-start{--anchor-h:span-right;& :where(.dropdown-content){inset-inline-end:auto;translate:0;[dir=rtl] &{translate:0}}&.dropdown-left{--anchor-h:left;--anchor-v:span-bottom;& .dropdown-content{top:0;bottom:auto}}&.dropdown-right{--anchor-h:right;--anchor-v:span-bottom;& .dropdown-content{top:0;bottom:auto}}}.\32 xl\:dropdown-center{--anchor-h:center;& :where(.dropdown-content){inset-inline-end:50%;translate:50%;[dir=rtl] &{translate:-50%}}&.dropdown-left{--anchor-h:left;--anchor-v:center;& .dropdown-content{top:auto;bottom:50%;translate:0 50%}}&.dropdown-right{--anchor-h:right;--anchor-v:center;& .dropdown-content{top:auto;bottom:50%;translate:0 50%}}}.\32 xl\:dropdown-end{--anchor-h:span-left;& :where(.dropdown-content){inset-inline-end:0;translate:0;[dir=rtl] &{translate:0}}&.dropdown-left{--anchor-h:left;--anchor-v:span-top;& .dropdown-content{top:auto;bottom:0}}&.dropdown-right{--anchor-h:right;--anchor-v:span-top;& .dropdown-content{top:auto;bottom:0}}}.\32 xl\:dropdown-left{--anchor-h:left;--anchor-v:span-bottom;& .dropdown-content{inset-inline-end:100%;transform-origin:100%;top:0;bottom:auto}}.\32 xl\:dropdown-right{--anchor-h:right;--anchor-v:span-bottom;& .dropdown-content{inset-inline-start:100%;transform-origin:0;top:0;bottom:auto}}.\32 xl\:dropdown-bottom{--anchor-v:bottom;& .dropdown-content{transform-origin:top;top:100%;bottom:auto}}.\32 xl\:dropdown-top{--anchor-v:top;& .dropdown-content{transform-origin:bottom;top:auto;bottom:100%}}}}