from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from .models import User
from core.models import Company, CompanyMembership


class UserRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for user registration"""
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    company_code = serializers.CharField(write_only=True, max_length=6)

    class Meta:
        model = User
        fields = ['email', 'username', 'first_name', 'last_name', 'phone_number', 
                 'password', 'password_confirm', 'company_code']

    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match.")
        
        # Validate company code
        try:
            company = Company.objects.get(company_code=attrs['company_code'], is_active=True)
        except Company.DoesNotExist:
            raise serializers.ValidationError("Invalid company code.")
        
        attrs['company'] = company
        return attrs

    def create(self, validated_data):
        company = validated_data.pop('company')
        validated_data.pop('password_confirm')
        validated_data.pop('company_code')
        
        user = User.objects.create_user(**validated_data)
        
        # Create company membership
        CompanyMembership.objects.create(
            user=user,
            company=company,
            role='user'
        )
        
        return user


class UserLoginSerializer(serializers.Serializer):
    """Serializer for user login"""
    email = serializers.EmailField()
    password = serializers.CharField()

    def validate(self, attrs):
        email = attrs.get('email')
        password = attrs.get('password')

        if email and password:
            user = authenticate(username=email, password=password)
            if not user:
                raise serializers.ValidationError('Invalid credentials.')
            if not user.is_active:
                raise serializers.ValidationError('User account is disabled.')
            attrs['user'] = user
        else:
            raise serializers.ValidationError('Must include email and password.')

        return attrs


class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer for user profile"""
    companies = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['user_id', 'email', 'username', 'first_name', 'last_name', 
                 'phone_number', 'date_joined', 'companies']
        read_only_fields = ['user_id', 'email', 'username', 'date_joined']

    def get_companies(self, obj):
        memberships = CompanyMembership.objects.filter(user=obj, is_active=True)
        return [
            {
                'company_id': membership.company.id,
                'company_name': membership.company.name,
                'company_code': membership.company.company_code,
                'role': membership.role,
                'joined_at': membership.joined_at
            }
            for membership in memberships
        ]


class PasswordChangeSerializer(serializers.Serializer):
    """Serializer for password change"""
    old_password = serializers.CharField()
    new_password = serializers.CharField(validators=[validate_password])
    new_password_confirm = serializers.CharField()

    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("New passwords don't match.")
        return attrs

    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("Old password is incorrect.")
        return value


class PasswordResetSerializer(serializers.Serializer):
    """Serializer for password reset request"""
    email = serializers.EmailField()

    def validate_email(self, value):
        try:
            User.objects.get(email=value, is_active=True)
        except User.DoesNotExist:
            raise serializers.ValidationError("No active user found with this email.")
        return value
