#!/usr/bin/env python
"""
Simple API test script for TaskSphere backend
Tests basic functionality of the API endpoints
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_login():
    """Test user login"""
    print("Testing login...")
    
    login_data = {
        "email": "<EMAIL>",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login/", json=login_data)
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Login successful!")
        print(f"User ID: {data['user']['user_id']}")
        print(f"Email: {data['user']['email']}")
        return data['tokens']['access']
    else:
        print("❌ Login failed!")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        return None

def test_profile(access_token):
    """Test getting user profile"""
    print("\nTesting profile retrieval...")
    
    headers = {"Authorization": f"Bearer {access_token}"}
    response = requests.get(f"{BASE_URL}/auth/profile/", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Profile retrieved successfully!")
        print(f"User: {data['first_name']} {data['last_name']}")
        print(f"Companies: {len(data['companies'])}")
        return data
    else:
        print("❌ Profile retrieval failed!")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        return None

def test_companies(access_token):
    """Test listing companies"""
    print("\nTesting companies list...")
    
    headers = {"Authorization": f"Bearer {access_token}"}
    response = requests.get(f"{BASE_URL}/companies/", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Companies retrieved successfully!")
        print(f"Response type: {type(data)}")

        # Handle paginated response
        if isinstance(data, dict) and 'results' in data:
            companies = data['results']
        else:
            companies = data

        print(f"Number of companies: {len(companies)}")
        if companies:
            company = companies[0]
            print(f"First company: {company['name']} ({company['company_code']})")
            return company['id']
        else:
            print("❌ No companies found!")
            return None
    else:
        print("❌ Companies retrieval failed!")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        return None

def test_tasks(access_token, company_id):
    """Test listing tasks"""
    print("\nTesting tasks list...")
    
    headers = {"Authorization": f"Bearer {access_token}"}
    params = {"company_id": company_id}
    response = requests.get(f"{BASE_URL}/tasks/", headers=headers, params=params)
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Tasks retrieved successfully!")

        # Handle paginated response
        if isinstance(data, dict) and 'results' in data:
            tasks = data['results']
        else:
            tasks = data

        print(f"Number of tasks: {len(tasks)}")
        if tasks:
            task = tasks[0]
            print(f"First task: {task['title']} - Status: {task['status']}")
    else:
        print("❌ Tasks retrieval failed!")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")

def test_notifications(access_token):
    """Test listing notifications"""
    print("\nTesting notifications list...")
    
    headers = {"Authorization": f"Bearer {access_token}"}
    response = requests.get(f"{BASE_URL}/notifications/", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Notifications retrieved successfully!")

        # Handle paginated response
        if isinstance(data, dict) and 'results' in data:
            notifications = data['results']
        else:
            notifications = data

        print(f"Number of notifications: {len(notifications)}")
        if notifications:
            notification = notifications[0]
            print(f"First notification: {notification['title']}")
    else:
        print("❌ Notifications retrieval failed!")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")

def main():
    """Run all tests"""
    print("🧪 Starting TaskSphere API Tests")
    print("=" * 50)
    
    # Test login
    access_token = test_login()
    if not access_token:
        print("❌ Cannot proceed without valid access token")
        return
    
    # Test profile
    profile = test_profile(access_token)
    if not profile:
        print("❌ Cannot proceed without valid profile")
        return
    
    # Test companies
    company_id = test_companies(access_token)
    if not company_id:
        print("❌ Cannot proceed without valid company")
        return
    
    # Test tasks
    test_tasks(access_token, company_id)
    
    # Test notifications
    test_notifications(access_token)
    
    print("\n" + "=" * 50)
    print("🎉 API Tests Completed!")
    print("=" * 50)

if __name__ == "__main__":
    try:
        main()
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure the Django server is running!")
        print("Run: python manage.py runserver")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
