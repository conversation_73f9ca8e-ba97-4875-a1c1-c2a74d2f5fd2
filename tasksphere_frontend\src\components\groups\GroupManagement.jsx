import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { useAuth } from '../../context/AuthContext';
import { groupsAPI } from '../../services/api';
import {
  Users,
  Plus,
  Edit,
  Trash2,
  UserPlus,
  UserMinus,
  AlertCircle,
} from 'lucide-react';
import toast from 'react-hot-toast';

const GroupManagement = () => {
  const { getPrimaryCompany, isAdmin } = useAuth();
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showAddUserForm, setShowAddUserForm] = useState(false);
  const queryClient = useQueryClient();
  const primaryCompany = getPrimaryCompany();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm();

  const {
    register: registerUser,
    handleSubmit: handleSubmitUser,
    reset: resetUser,
    formState: { errors: userErrors },
  } = useForm();

  // Fetch groups
  const { data: groupsResponse = [], isLoading, error } = useQuery({
    queryKey: ['groups', primaryCompany?.company_id],
    queryFn: () => {
      console.log('Fetching groups for company:', primaryCompany?.company_id);
      return groupsAPI.getGroups(primaryCompany?.company_id);
    },
    enabled: !!primaryCompany?.company_id,
  });

  const groups = Array.isArray(groupsResponse) ? groupsResponse : (groupsResponse?.results || []);

  // Create group mutation
  const createGroupMutation = useMutation({
    mutationFn: groupsAPI.createGroup,
    onSuccess: (data) => {
      console.log('Group created successfully:', data);
      queryClient.invalidateQueries(['groups']);
      toast.success('Group created successfully!');
      setShowCreateForm(false);
      reset();
    },
    onError: (error) => {
      console.error('Create group error:', error);
      console.error('Error response:', error.response);
      console.error('Error status:', error.response?.status);
      console.error('Error data:', error.response?.data);
      console.error('Error headers:', error.response?.headers);

      let errorMessage = 'Failed to create group.';

      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error;
        } else if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message;
        } else {
          errorMessage = JSON.stringify(error.response.data);
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
    },
  });

  // Add user to group mutation
  const addUserMutation = useMutation({
    mutationFn: ({ groupId, userId }) => groupsAPI.addUserToGroup(groupId, userId),
    onSuccess: () => {
      queryClient.invalidateQueries(['groups']);
      toast.success('User added to group successfully!');
      setShowAddUserForm(false);
      resetUser();
    },
    onError: (error) => {
      console.error('Add user error:', error);
      toast.error('Failed to add user to group.');
    },
  });

  // Remove user from group mutation
  const removeUserMutation = useMutation({
    mutationFn: ({ groupId, userId }) => groupsAPI.removeUserFromGroup(groupId, userId),
    onSuccess: () => {
      queryClient.invalidateQueries(['groups']);
      toast.success('User removed from group successfully!');
    },
    onError: (error) => {
      console.error('Remove user error:', error);
      toast.error('Failed to remove user from group.');
    },
  });

  const onCreateGroup = (data) => {
    console.log('Creating group with data:', data);
    console.log('Primary company:', primaryCompany);

    // Validate required fields
    if (!data.name || !data.name.trim()) {
      toast.error('Group name is required');
      return;
    }

    if (!primaryCompany?.company_id) {
      toast.error('No company selected');
      return;
    }

    const groupData = {
      name: data.name.trim(),
      description: data.description?.trim() || '',
      company: primaryCompany.company_id,
    };

    console.log('Final group data:', groupData);
    console.log('Mutation function:', createGroupMutation.mutate);

    createGroupMutation.mutate(groupData);
  };

  const onAddUser = (data) => {
    if (selectedGroup) {
      addUserMutation.mutate({
        groupId: selectedGroup.id,
        userId: data.user_id,
      });
    }
  };

  const handleRemoveUser = (groupId, userId) => {
    if (confirm('Are you sure you want to remove this user from the group?')) {
      removeUserMutation.mutate({ groupId, userId });
    }
  };

  if (!isAdmin()) {
    return (
      <div className="alert alert-warning">
        <AlertCircle className="h-5 w-5" />
        <span>Only admins can manage groups. Current admin status: {isAdmin().toString()}</span>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-error">
        <AlertCircle className="h-5 w-5" />
        <span>Failed to load groups. Please try again.</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Group Management</h1>
          <p className="text-base-content/70 mt-1">
            Manage teams and organize users within your company
          </p>
        </div>
        <div className="flex space-x-2">
          <button
            className="btn btn-primary"
            onClick={() => setShowCreateForm(true)}
          >
            <Plus className="h-5 w-5 mr-2" />
            Create Group
          </button>
          <button
            className="btn btn-outline"
            onClick={() => {
              console.log('Testing API call...');
              const testData = {
                name: 'Test Group',
                description: 'Test description',
                company: primaryCompany?.company_id,
              };
              console.log('Test data:', testData);
              createGroupMutation.mutate(testData);
            }}
          >
            Test API
          </button>
        </div>
      </div>

      {/* Create Group Form */}
      {showCreateForm && (
        <div className="card bg-base-100 shadow-sm">
          <div className="card-body">
            <h3 className="card-title">Create New Group</h3>
            <form onSubmit={handleSubmit(onCreateGroup)} className="space-y-4">
              <div className="text-sm text-gray-600 mb-4">
                Debug: Company ID = {primaryCompany?.company_id}, User Admin = {isAdmin().toString()}
              </div>
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Group Name *</span>
                </label>
                <input
                  type="text"
                  placeholder="Enter group name"
                  className={`input input-bordered ${errors.name ? 'input-error' : ''}`}
                  {...register('name', { required: 'Group name is required' })}
                />
                {errors.name && (
                  <label className="label">
                    <span className="label-text-alt text-error">{errors.name.message}</span>
                  </label>
                )}
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">Description</span>
                </label>
                <textarea
                  placeholder="Enter group description"
                  className="textarea textarea-bordered"
                  {...register('description')}
                />
              </div>

              <div className="flex justify-end space-x-2">
                <button
                  type="button"
                  className="btn btn-ghost"
                  onClick={() => {
                    setShowCreateForm(false);
                    reset();
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className={`btn btn-primary ${createGroupMutation.isLoading ? 'loading' : ''}`}
                  disabled={createGroupMutation.isLoading}
                >
                  Create Group
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Groups List */}
      {groups.length === 0 ? (
        <div className="text-center py-12">
          <Users className="h-16 w-16 mx-auto mb-4 text-base-content/30" />
          <h3 className="text-lg font-medium mb-2">No groups found</h3>
          <p className="text-base-content/70">
            Create your first group to organize users
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {groups.map((group) => (
            <div key={group.id} className="card bg-base-100 shadow-sm hover:shadow-md transition-shadow">
              <div className="card-body">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="card-title text-lg">{group.name}</h3>
                  <div className="dropdown dropdown-end">
                    <label tabIndex={0} className="btn btn-ghost btn-sm">
                      <Edit className="h-4 w-4" />
                    </label>
                    <ul tabIndex={0} className="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-52">
                      <li>
                        <a onClick={() => {
                          setSelectedGroup(group);
                          setShowAddUserForm(true);
                        }}>
                          <UserPlus className="h-4 w-4" />
                          Add User
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>

                {group.description && (
                  <p className="text-sm text-base-content/70 mb-4">{group.description}</p>
                )}

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Members</span>
                    <span className="badge badge-primary badge-sm">{group.member_count || 0}</span>
                  </div>
                  
                  <div className="text-xs text-base-content/70">
                    Created by: {group.created_by_name}
                  </div>
                </div>

                <div className="card-actions justify-end mt-4">
                  <button
                    className="btn btn-primary btn-sm"
                    onClick={() => {
                      setSelectedGroup(group);
                      setShowAddUserForm(true);
                    }}
                  >
                    <UserPlus className="h-4 w-4 mr-1" />
                    Add User
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add User Modal */}
      {showAddUserForm && selectedGroup && (
        <div className="modal modal-open">
          <div className="modal-box">
            <h3 className="font-bold text-lg mb-4">
              Add User to {selectedGroup.name}
            </h3>
            
            <form onSubmit={handleSubmitUser(onAddUser)} className="space-y-4">
              <div className="form-control">
                <label className="label">
                  <span className="label-text">User ID *</span>
                </label>
                <input
                  type="text"
                  placeholder="Enter 8-digit User ID"
                  className={`input input-bordered ${userErrors.user_id ? 'input-error' : ''}`}
                  {...registerUser('user_id', {
                    required: 'User ID is required',
                    pattern: {
                      value: /^[A-Z0-9]{8}$/,
                      message: 'User ID must be 8 alphanumeric characters',
                    },
                  })}
                />
                {userErrors.user_id && (
                  <label className="label">
                    <span className="label-text-alt text-error">{userErrors.user_id.message}</span>
                  </label>
                )}
                <label className="label">
                  <span className="label-text-alt">
                    Enter the 8-digit User ID (e.g., ABC12345)
                  </span>
                </label>
              </div>

              <div className="modal-action">
                <button
                  type="button"
                  className="btn btn-ghost"
                  onClick={() => {
                    setShowAddUserForm(false);
                    setSelectedGroup(null);
                    resetUser();
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className={`btn btn-primary ${addUserMutation.isLoading ? 'loading' : ''}`}
                  disabled={addUserMutation.isLoading}
                >
                  Add User
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default GroupManagement;
