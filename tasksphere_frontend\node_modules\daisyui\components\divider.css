/*! 🌼 daisyUI 5.0.45 - MIT License */ @layer utilities{.divider{white-space:nowrap;height:1rem;margin:var(--divider-m,1rem 0);--divider-color:color-mix(in oklab,var(--color-base-content)10%,transparent);flex-direction:row;align-self:stretch;align-items:center;display:flex;&:before,&:after{content:"";background-color:var(--divider-color);flex-grow:1;width:100%;height:.125rem}@media print{&:before,&:after{border:.5px solid}}&:not(:empty){gap:1rem}}.divider-horizontal{--divider-m:0 1rem;&.divider{flex-direction:column;width:1rem;height:auto;&:before,&:after{width:.125rem;height:100%}}}.divider-vertical{--divider-m:1rem 0;&.divider{flex-direction:row;width:auto;height:1rem;&:before,&:after{width:100%;height:.125rem}}}.divider-neutral{&:before,&:after{background-color:var(--color-neutral)}}.divider-primary{&:before,&:after{background-color:var(--color-primary)}}.divider-secondary{&:before,&:after{background-color:var(--color-secondary)}}.divider-accent{&:before,&:after{background-color:var(--color-accent)}}.divider-success{&:before,&:after{background-color:var(--color-success)}}.divider-warning{&:before,&:after{background-color:var(--color-warning)}}.divider-info{&:before,&:after{background-color:var(--color-info)}}.divider-error{&:before,&:after{background-color:var(--color-error)}}.divider-start:before,.divider-end:after{display:none}@media (width>=640px){.sm\:divider{white-space:nowrap;height:1rem;margin:var(--divider-m,1rem 0);--divider-color:color-mix(in oklab,var(--color-base-content)10%,transparent);flex-direction:row;align-self:stretch;align-items:center;display:flex;&:before,&:after{content:"";background-color:var(--divider-color);flex-grow:1;width:100%;height:.125rem}@media print{&:before,&:after{border:.5px solid}}&:not(:empty){gap:1rem}}.sm\:divider-horizontal{--divider-m:0 1rem;&.divider{flex-direction:column;width:1rem;height:auto;&:before,&:after{width:.125rem;height:100%}}}.sm\:divider-vertical{--divider-m:1rem 0;&.divider{flex-direction:row;width:auto;height:1rem;&:before,&:after{width:100%;height:.125rem}}}.sm\:divider-neutral{&:before,&:after{background-color:var(--color-neutral)}}.sm\:divider-primary{&:before,&:after{background-color:var(--color-primary)}}.sm\:divider-secondary{&:before,&:after{background-color:var(--color-secondary)}}.sm\:divider-accent{&:before,&:after{background-color:var(--color-accent)}}.sm\:divider-success{&:before,&:after{background-color:var(--color-success)}}.sm\:divider-warning{&:before,&:after{background-color:var(--color-warning)}}.sm\:divider-info{&:before,&:after{background-color:var(--color-info)}}.sm\:divider-error{&:before,&:after{background-color:var(--color-error)}}.sm\:divider-start:before,.sm\:divider-end:after{display:none}}@media (width>=768px){.md\:divider{white-space:nowrap;height:1rem;margin:var(--divider-m,1rem 0);--divider-color:color-mix(in oklab,var(--color-base-content)10%,transparent);flex-direction:row;align-self:stretch;align-items:center;display:flex;&:before,&:after{content:"";background-color:var(--divider-color);flex-grow:1;width:100%;height:.125rem}@media print{&:before,&:after{border:.5px solid}}&:not(:empty){gap:1rem}}.md\:divider-horizontal{--divider-m:0 1rem;&.divider{flex-direction:column;width:1rem;height:auto;&:before,&:after{width:.125rem;height:100%}}}.md\:divider-vertical{--divider-m:1rem 0;&.divider{flex-direction:row;width:auto;height:1rem;&:before,&:after{width:100%;height:.125rem}}}.md\:divider-neutral{&:before,&:after{background-color:var(--color-neutral)}}.md\:divider-primary{&:before,&:after{background-color:var(--color-primary)}}.md\:divider-secondary{&:before,&:after{background-color:var(--color-secondary)}}.md\:divider-accent{&:before,&:after{background-color:var(--color-accent)}}.md\:divider-success{&:before,&:after{background-color:var(--color-success)}}.md\:divider-warning{&:before,&:after{background-color:var(--color-warning)}}.md\:divider-info{&:before,&:after{background-color:var(--color-info)}}.md\:divider-error{&:before,&:after{background-color:var(--color-error)}}.md\:divider-start:before,.md\:divider-end:after{display:none}}@media (width>=1024px){.lg\:divider{white-space:nowrap;height:1rem;margin:var(--divider-m,1rem 0);--divider-color:color-mix(in oklab,var(--color-base-content)10%,transparent);flex-direction:row;align-self:stretch;align-items:center;display:flex;&:before,&:after{content:"";background-color:var(--divider-color);flex-grow:1;width:100%;height:.125rem}@media print{&:before,&:after{border:.5px solid}}&:not(:empty){gap:1rem}}.lg\:divider-horizontal{--divider-m:0 1rem;&.divider{flex-direction:column;width:1rem;height:auto;&:before,&:after{width:.125rem;height:100%}}}.lg\:divider-vertical{--divider-m:1rem 0;&.divider{flex-direction:row;width:auto;height:1rem;&:before,&:after{width:100%;height:.125rem}}}.lg\:divider-neutral{&:before,&:after{background-color:var(--color-neutral)}}.lg\:divider-primary{&:before,&:after{background-color:var(--color-primary)}}.lg\:divider-secondary{&:before,&:after{background-color:var(--color-secondary)}}.lg\:divider-accent{&:before,&:after{background-color:var(--color-accent)}}.lg\:divider-success{&:before,&:after{background-color:var(--color-success)}}.lg\:divider-warning{&:before,&:after{background-color:var(--color-warning)}}.lg\:divider-info{&:before,&:after{background-color:var(--color-info)}}.lg\:divider-error{&:before,&:after{background-color:var(--color-error)}}.lg\:divider-start:before,.lg\:divider-end:after{display:none}}@media (width>=1280px){.xl\:divider{white-space:nowrap;height:1rem;margin:var(--divider-m,1rem 0);--divider-color:color-mix(in oklab,var(--color-base-content)10%,transparent);flex-direction:row;align-self:stretch;align-items:center;display:flex;&:before,&:after{content:"";background-color:var(--divider-color);flex-grow:1;width:100%;height:.125rem}@media print{&:before,&:after{border:.5px solid}}&:not(:empty){gap:1rem}}.xl\:divider-horizontal{--divider-m:0 1rem;&.divider{flex-direction:column;width:1rem;height:auto;&:before,&:after{width:.125rem;height:100%}}}.xl\:divider-vertical{--divider-m:1rem 0;&.divider{flex-direction:row;width:auto;height:1rem;&:before,&:after{width:100%;height:.125rem}}}.xl\:divider-neutral{&:before,&:after{background-color:var(--color-neutral)}}.xl\:divider-primary{&:before,&:after{background-color:var(--color-primary)}}.xl\:divider-secondary{&:before,&:after{background-color:var(--color-secondary)}}.xl\:divider-accent{&:before,&:after{background-color:var(--color-accent)}}.xl\:divider-success{&:before,&:after{background-color:var(--color-success)}}.xl\:divider-warning{&:before,&:after{background-color:var(--color-warning)}}.xl\:divider-info{&:before,&:after{background-color:var(--color-info)}}.xl\:divider-error{&:before,&:after{background-color:var(--color-error)}}.xl\:divider-start:before,.xl\:divider-end:after{display:none}}@media (width>=1536px){.\32 xl\:divider{white-space:nowrap;height:1rem;margin:var(--divider-m,1rem 0);--divider-color:color-mix(in oklab,var(--color-base-content)10%,transparent);flex-direction:row;align-self:stretch;align-items:center;display:flex;&:before,&:after{content:"";background-color:var(--divider-color);flex-grow:1;width:100%;height:.125rem}@media print{&:before,&:after{border:.5px solid}}&:not(:empty){gap:1rem}}.\32 xl\:divider-horizontal{--divider-m:0 1rem;&.divider{flex-direction:column;width:1rem;height:auto;&:before,&:after{width:.125rem;height:100%}}}.\32 xl\:divider-vertical{--divider-m:1rem 0;&.divider{flex-direction:row;width:auto;height:1rem;&:before,&:after{width:100%;height:.125rem}}}.\32 xl\:divider-neutral{&:before,&:after{background-color:var(--color-neutral)}}.\32 xl\:divider-primary{&:before,&:after{background-color:var(--color-primary)}}.\32 xl\:divider-secondary{&:before,&:after{background-color:var(--color-secondary)}}.\32 xl\:divider-accent{&:before,&:after{background-color:var(--color-accent)}}.\32 xl\:divider-success{&:before,&:after{background-color:var(--color-success)}}.\32 xl\:divider-warning{&:before,&:after{background-color:var(--color-warning)}}.\32 xl\:divider-info{&:before,&:after{background-color:var(--color-info)}}.\32 xl\:divider-error{&:before,&:after{background-color:var(--color-error)}}.\32 xl\:divider-start:before,.\32 xl\:divider-end:after{display:none}}}