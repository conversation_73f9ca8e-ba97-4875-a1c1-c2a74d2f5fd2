import { useState } from 'react';
import { authAPI } from '../../services/api';
import { CheckCircle, XCircle, Loader } from 'lucide-react';

const ApiTest = () => {
  const [testResults, setTestResults] = useState({});
  const [testing, setTesting] = useState(false);

  const runTests = async () => {
    setTesting(true);
    const results = {};

    // Test 1: Backend connectivity
    try {
      const response = await fetch('http://127.0.0.1:8000/api/auth/login/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: 'test', password: 'test' })
      });
      results.connectivity = response.status === 400 ? 'success' : 'error'; // 400 is expected for invalid credentials
    } catch (error) {
      console.error('Connectivity test error:', error);
      results.connectivity = 'error';
    }

    // Test 2: Login with demo credentials
    try {
      const loginResult = await authAPI.login({
        email: '<EMAIL>',
        password: 'admin123'
      });
      console.log('Login result:', loginResult);
      results.login = 'success';
    } catch (error) {
      console.error('Login test error:', error);
      results.login = 'error';
    }

    // Test 3: Profile fetch
    try {
      const profile = await authAPI.getProfile();
      console.log('Profile result:', profile);
      results.profile = 'success';
    } catch (error) {
      console.error('Profile test error:', error);
      results.profile = 'error';
    }

    setTestResults(results);
    setTesting(false);
  };

  const TestResult = ({ test, result }) => (
    <div className="flex items-center justify-between p-3 bg-base-200 rounded-lg">
      <span className="font-medium">{test}</span>
      {result === 'success' && <CheckCircle className="h-5 w-5 text-success" />}
      {result === 'error' && <XCircle className="h-5 w-5 text-error" />}
      {result === undefined && <div className="w-5 h-5 bg-base-300 rounded-full"></div>}
    </div>
  );

  return (
    <div className="card bg-base-100 shadow-sm max-w-md mx-auto">
      <div className="card-body">
        <h2 className="card-title">API Connection Test</h2>
        <p className="text-base-content/70 mb-4">
          Test the connection between frontend and backend
        </p>

        <div className="space-y-3">
          <TestResult test="Backend Connectivity" result={testResults.connectivity} />
          <TestResult test="Login API" result={testResults.login} />
          <TestResult test="Profile API" result={testResults.profile} />
        </div>

        <div className="card-actions justify-end mt-6">
          <button
            className={`btn btn-primary ${testing ? 'loading' : ''}`}
            onClick={runTests}
            disabled={testing}
          >
            {testing ? (
              <>
                <Loader className="h-4 w-4 mr-2 animate-spin" />
                Testing...
              </>
            ) : (
              'Run Tests'
            )}
          </button>
        </div>

        {Object.keys(testResults).length > 0 && (
          <div className="mt-4 p-3 bg-base-200 rounded-lg">
            <p className="text-sm">
              <strong>Results:</strong> {Object.values(testResults).filter(r => r === 'success').length}/3 tests passed
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ApiTest;
