:root:has(input.theme-controller[value=cmyk]:checked),[data-theme="cmyk"] {
color-scheme: light;
--color-base-100: oklch(100% 0 0);
--color-base-200: oklch(95% 0 0);
--color-base-300: oklch(90% 0 0);
--color-base-content: oklch(20% 0 0);
--color-primary: oklch(71.772% 0.133 239.443);
--color-primary-content: oklch(14.354% 0.026 239.443);
--color-secondary: oklch(64.476% 0.202 359.339);
--color-secondary-content: oklch(12.895% 0.04 359.339);
--color-accent: oklch(94.228% 0.189 105.306);
--color-accent-content: oklch(18.845% 0.037 105.306);
--color-neutral: oklch(21.778% 0 0);
--color-neutral-content: oklch(84.355% 0 0);
--color-info: oklch(68.475% 0.094 217.284);
--color-info-content: oklch(13.695% 0.018 217.284);
--color-success: oklch(46.949% 0.162 321.406);
--color-success-content: oklch(89.389% 0.032 321.406);
--color-warning: oklch(71.236% 0.159 52.023);
--color-warning-content: oklch(14.247% 0.031 52.023);
--color-error: oklch(62.013% 0.208 28.717);
--color-error-content: oklch(12.402% 0.041 28.717);
--radius-selector: 1rem;
--radius-field: 0.5rem;
--radius-box: 1rem;
--size-selector: 0.25rem;
--size-field: 0.25rem;
--border: 1px;
--depth: 0;
--noise: 0;
}
