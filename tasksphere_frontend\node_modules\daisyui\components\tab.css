/*! 🌼 daisyUI 5.0.45 - MIT License */ @layer utilities{.tabs{--tabs-height:auto;--tabs-direction:row;--tab-height:calc(var(--size-field,.25rem)*10);height:var(--tabs-height);flex-wrap:wrap;flex-direction:var(--tabs-direction);display:flex}.tab{cursor:pointer;appearance:none;text-align:center;-webkit-user-select:none;user-select:none;--tab-p:1rem;--tab-bg:var(--color-base-100);--tab-border-color:var(--color-base-300);--tab-radius-ss:0;--tab-radius-se:0;--tab-radius-es:0;--tab-radius-ee:0;--tab-order:0;--tab-radius-min:calc(.75rem - var(--border));flex-wrap:wrap;order:var(--tab-order);height:var(--tab-height);border-color:#0000;justify-content:center;align-items:center;padding-inline-start:var(--tab-p);padding-inline-end:var(--tab-p);font-size:.875rem;display:inline-flex;position:relative;&:hover{@media (hover:hover){&{color:var(--color-base-content)}}}&:is(input[type=radio]){min-width:fit-content;&:after{content:attr(aria-label)}}&:is(label){position:relative;& input{cursor:pointer;appearance:none;opacity:0;position:absolute;inset:0}}&:checked,&:is(label:has(:checked)),&:is(.tab-active,[aria-selected=true]){&+.tab-content{height:calc(100% - var(--tab-height) + var(--border));display:block}}&:not(:checked,label:has(:checked),:hover,.tab-active,[aria-selected=true]){color:color-mix(in oklab,var(--color-base-content)50%,transparent)}&:not(input):empty{cursor:default;flex-grow:1}&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible,&:is(label:has(:checked:focus-visible)){outline-offset:-5px;outline:2px solid}&[disabled]{pointer-events:none;opacity:.4}}.tab-disabled{pointer-events:none;opacity:.4}.tabs-border{& .tab{--tab-border-color:#0000 #0000 var(--tab-border-color)#0000;border-radius:var(--radius-field);position:relative;&:before{--tw-content:"";content:var(--tw-content);background-color:var(--tab-border-color);border-radius:var(--radius-field);width:80%;height:3px;transition:background-color .2s;position:absolute;bottom:0;left:10%}&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked),&:is(label:has(:checked)){&:before{--tab-border-color:currentColor;border-top:3px solid}}}}.tabs-lift{--tabs-height:auto;--tabs-direction:row;&>.tab{--tab-border:0 0 var(--border)0;--tab-radius-ss:min(var(--radius-field),var(--tab-radius-min));--tab-radius-se:min(var(--radius-field),var(--tab-radius-min));--tab-radius-es:0;--tab-radius-ee:0;--tab-paddings:var(--border)var(--tab-p)0 var(--tab-p);--tab-border-colors:#0000 #0000 var(--tab-border-color)#0000;--tab-corner-width:calc(100% + min(var(--radius-field),var(--tab-radius-min))*2);--tab-corner-height:min(var(--radius-field),var(--tab-radius-min));--tab-corner-position:top left,top right;border-width:var(--tab-border);padding:var(--tab-paddings);border-color:var(--tab-border-colors);border-start-start-radius:var(--tab-radius-ss);border-start-end-radius:var(--tab-radius-se);border-end-end-radius:var(--tab-radius-ee);border-end-start-radius:var(--tab-radius-es);&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked,label:has(:checked)){--tab-border:var(--border)var(--border)0 var(--border);--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color);--tab-paddings:0 calc(var(--tab-p) - var(--border))var(--border)calc(var(--tab-p) - var(--border));--tab-inset:auto auto 0 auto;--tab-grad:calc(69% - var(--border));--radius-start:radial-gradient(circle at top left,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));--radius-end:radial-gradient(circle at top right,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));background-color:var(--tab-bg);&:before{z-index:1;content:"";width:var(--tab-corner-width);height:var(--tab-corner-height);background-position:var(--tab-corner-position);background-image:var(--radius-start),var(--radius-end);background-size:min(var(--radius-field),var(--tab-radius-min))min(var(--radius-field),var(--tab-radius-min));inset:var(--tab-inset);background-repeat:no-repeat;display:block;position:absolute}&:first-child:before{--radius-start:none}[dir=rtl] &:first-child:before{transform:rotateY(180deg)}&:last-child:before{--radius-end:none}[dir=rtl] &:last-child:before{transform:rotateY(180deg)}}}&:has(.tab-content){&>.tab:first-child{&:not(.tab-active,[aria-selected=true]){--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color)}}}& .tab-content{--tabcontent-margin:calc(-1*var(--border))0 0 0;--tabcontent-radius-ss:0;--tabcontent-radius-se:var(--radius-box);--tabcontent-radius-es:var(--radius-box);--tabcontent-radius-ee:var(--radius-box)}& :checked,& label:has(:checked),& :is(.tab-active,[aria-selected=true]){&+.tab-content{&:first-child,&:nth-child(n+3){--tabcontent-radius-ss:var(--radius-box)}}}}.tabs-top{--tabs-height:auto;--tabs-direction:row;& .tab{--tab-order:0;--tab-border:0 0 var(--border)0;--tab-radius-ss:min(var(--radius-field),var(--tab-radius-min));--tab-radius-se:min(var(--radius-field),var(--tab-radius-min));--tab-radius-es:0;--tab-radius-ee:0;--tab-paddings:var(--border)var(--tab-p)0 var(--tab-p);--tab-border-colors:#0000 #0000 var(--tab-border-color)#0000;--tab-corner-width:calc(100% + min(var(--radius-field),var(--tab-radius-min))*2);--tab-corner-height:min(var(--radius-field),var(--tab-radius-min));--tab-corner-position:top left,top right;&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked),&:is(label:has(:checked)){--tab-border:var(--border)var(--border)0 var(--border);--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color);--tab-paddings:0 calc(var(--tab-p) - var(--border))var(--border)calc(var(--tab-p) - var(--border));--tab-inset:auto auto 0 auto;--radius-start:radial-gradient(circle at top left,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));--radius-end:radial-gradient(circle at top right,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px))}}&:has(.tab-content){&>.tab:first-child{&:not(.tab-active,[aria-selected=true]){--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color)}}}& .tab-content{--tabcontent-order:1;--tabcontent-margin:calc(-1*var(--border))0 0 0;--tabcontent-radius-ss:0;--tabcontent-radius-se:var(--radius-box);--tabcontent-radius-es:var(--radius-box);--tabcontent-radius-ee:var(--radius-box)}& :checked,& label:has(:checked),& :is(.tab-active,[aria-selected=true]){&+.tab-content{&:first-child,&:nth-child(n+3){--tabcontent-radius-ss:var(--radius-box)}}}}.tabs-bottom{--tabs-height:auto;--tabs-direction:row;& .tab{--tab-order:1;--tab-border:var(--border)0 0 0;--tab-radius-ss:0;--tab-radius-se:0;--tab-radius-es:min(var(--radius-field),var(--tab-radius-min));--tab-radius-ee:min(var(--radius-field),var(--tab-radius-min));--tab-border-colors:var(--tab-border-color)#0000 #0000 #0000;--tab-paddings:0 var(--tab-p)var(--border)var(--tab-p);--tab-corner-width:calc(100% + min(var(--radius-field),var(--tab-radius-min))*2);--tab-corner-height:min(var(--radius-field),var(--tab-radius-min));--tab-corner-position:top left,top right;&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked),&:is(label:has(:checked)){--tab-border:0 var(--border)var(--border)var(--border);--tab-border-colors:#0000 var(--tab-border-color)var(--tab-border-color)var(--tab-border-color);--tab-paddings:var(--border)calc(var(--tab-p) - var(--border))0 calc(var(--tab-p) - var(--border));--tab-inset:0 auto auto auto;--radius-start:radial-gradient(circle at bottom left,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));--radius-end:radial-gradient(circle at bottom right,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px))}}&:has(.tab-content){&>.tab:first-child{&:not(.tab-active,[aria-selected=true]){--tab-border-colors:#0000 var(--tab-border-color)var(--tab-border-color)var(--tab-border-color)}}}& .tab-content{--tabcontent-order:0;--tabcontent-margin:0 0 calc(-1*var(--border))0;--tabcontent-radius-ss:var(--radius-box);--tabcontent-radius-se:var(--radius-box);--tabcontent-radius-es:0;--tabcontent-radius-ee:var(--radius-box)}&>:checked,&>:is(label:has(:checked)),&>:is(.tab-active,[aria-selected=true]){&+.tab-content:not(:nth-child(2)){--tabcontent-radius-es:var(--radius-box)}}}.tabs-box{background-color:var(--color-base-200);--tabs-box-radius:calc(var(--radius-field) + var(--radius-field) + var(--radius-field));border-radius:calc(var(--radius-field) + min(.25rem,var(--tabs-box-radius)));box-shadow:0 -.5px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 .5px oklch(0% 0 0/calc(var(--depth)*.05))inset;padding:.25rem;& .tab{border-radius:var(--radius-field);border-style:none;&:focus-visible,&:is(label:has(:checked:focus-visible)){outline-offset:2px}}&>:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&>:is(input:checked),&>:is(label:has(:checked)){background-color:var(--tab-bg,var(--color-base-100));box-shadow:0 1px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px 1px -1px color-mix(in oklab,var(--color-neutral)calc(var(--depth)*50%),#0000),0 1px 6px -4px color-mix(in oklab,var(--color-neutral)calc(var(--depth)*100%),#0000);@media (forced-colors:active){&{border:1px solid}}}}.tab-content{--tabcontent-radius-ss:0;--tabcontent-radius-se:0;--tabcontent-radius-es:0;--tabcontent-radius-ee:0;--tabcontent-order:1;width:100%;margin:var(--tabcontent-margin);order:1;order:var(--tabcontent-order);border-color:#0000;border-width:var(--border);border-start-start-radius:var(--tabcontent-radius-ss);border-start-end-radius:var(--tabcontent-radius-se);border-end-end-radius:var(--tabcontent-radius-ee);border-end-start-radius:var(--tabcontent-radius-es);display:none}.tabs-xs{--tab-height:calc(var(--size-field,.25rem)*6);& :where(.tab){--tab-p:.375rem;--tab-radius-min:calc(.5rem - var(--border));font-size:.75rem}}.tabs-sm{--tab-height:calc(var(--size-field,.25rem)*8);& :where(.tab){--tab-p:.5rem;--tab-radius-min:calc(.5rem - var(--border));font-size:.875rem}}.tabs-md{--tab-height:calc(var(--size-field,.25rem)*10);& :where(.tab){--tab-p:.75rem;--tab-radius-min:calc(.75rem - var(--border));font-size:.875rem}}.tabs-lg{--tab-height:calc(var(--size-field,.25rem)*12);& :where(.tab){--tab-p:1rem;--tab-radius-min:calc(1.5rem - var(--border));font-size:1.125rem}}.tabs-xl{--tab-height:calc(var(--size-field,.25rem)*14);& :where(.tab){--tab-p:1.25rem;--tab-radius-min:calc(2rem - var(--border));font-size:1.125rem}}@media (width>=640px){.sm\:tabs{--tabs-height:auto;--tabs-direction:row;--tab-height:calc(var(--size-field,.25rem)*10);height:var(--tabs-height);flex-wrap:wrap;flex-direction:var(--tabs-direction);display:flex}.sm\:tab{cursor:pointer;appearance:none;text-align:center;-webkit-user-select:none;user-select:none;--tab-p:1rem;--tab-bg:var(--color-base-100);--tab-border-color:var(--color-base-300);--tab-radius-ss:0;--tab-radius-se:0;--tab-radius-es:0;--tab-radius-ee:0;--tab-order:0;--tab-radius-min:calc(.75rem - var(--border));flex-wrap:wrap;order:var(--tab-order);height:var(--tab-height);border-color:#0000;justify-content:center;align-items:center;padding-inline-start:var(--tab-p);padding-inline-end:var(--tab-p);font-size:.875rem;display:inline-flex;position:relative;&:hover{@media (hover:hover){&{color:var(--color-base-content)}}}&:is(input[type=radio]){min-width:fit-content;&:after{content:attr(aria-label)}}&:is(label){position:relative;& input{cursor:pointer;appearance:none;opacity:0;position:absolute;inset:0}}&:checked,&:is(label:has(:checked)),&:is(.tab-active,[aria-selected=true]){&+.tab-content{height:calc(100% - var(--tab-height) + var(--border));display:block}}&:not(:checked,label:has(:checked),:hover,.tab-active,[aria-selected=true]){color:color-mix(in oklab,var(--color-base-content)50%,transparent)}&:not(input):empty{cursor:default;flex-grow:1}&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible,&:is(label:has(:checked:focus-visible)){outline-offset:-5px;outline:2px solid}&[disabled]{pointer-events:none;opacity:.4}}.sm\:tab-disabled{pointer-events:none;opacity:.4}.sm\:tabs-border{& .tab{--tab-border-color:#0000 #0000 var(--tab-border-color)#0000;border-radius:var(--radius-field);position:relative;&:before{--tw-content:"";content:var(--tw-content);background-color:var(--tab-border-color);border-radius:var(--radius-field);width:80%;height:3px;transition:background-color .2s;position:absolute;bottom:0;left:10%}&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked),&:is(label:has(:checked)){&:before{--tab-border-color:currentColor;border-top:3px solid}}}}.sm\:tabs-lift{--tabs-height:auto;--tabs-direction:row;&>.tab{--tab-border:0 0 var(--border)0;--tab-radius-ss:min(var(--radius-field),var(--tab-radius-min));--tab-radius-se:min(var(--radius-field),var(--tab-radius-min));--tab-radius-es:0;--tab-radius-ee:0;--tab-paddings:var(--border)var(--tab-p)0 var(--tab-p);--tab-border-colors:#0000 #0000 var(--tab-border-color)#0000;--tab-corner-width:calc(100% + min(var(--radius-field),var(--tab-radius-min))*2);--tab-corner-height:min(var(--radius-field),var(--tab-radius-min));--tab-corner-position:top left,top right;border-width:var(--tab-border);padding:var(--tab-paddings);border-color:var(--tab-border-colors);border-start-start-radius:var(--tab-radius-ss);border-start-end-radius:var(--tab-radius-se);border-end-end-radius:var(--tab-radius-ee);border-end-start-radius:var(--tab-radius-es);&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked,label:has(:checked)){--tab-border:var(--border)var(--border)0 var(--border);--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color);--tab-paddings:0 calc(var(--tab-p) - var(--border))var(--border)calc(var(--tab-p) - var(--border));--tab-inset:auto auto 0 auto;--tab-grad:calc(69% - var(--border));--radius-start:radial-gradient(circle at top left,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));--radius-end:radial-gradient(circle at top right,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));background-color:var(--tab-bg);&:before{z-index:1;content:"";width:var(--tab-corner-width);height:var(--tab-corner-height);background-position:var(--tab-corner-position);background-image:var(--radius-start),var(--radius-end);background-size:min(var(--radius-field),var(--tab-radius-min))min(var(--radius-field),var(--tab-radius-min));inset:var(--tab-inset);background-repeat:no-repeat;display:block;position:absolute}&:first-child:before{--radius-start:none}[dir=rtl] &:first-child:before{transform:rotateY(180deg)}&:last-child:before{--radius-end:none}[dir=rtl] &:last-child:before{transform:rotateY(180deg)}}}&:has(.tab-content){&>.tab:first-child{&:not(.tab-active,[aria-selected=true]){--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color)}}}& .tab-content{--tabcontent-margin:calc(-1*var(--border))0 0 0;--tabcontent-radius-ss:0;--tabcontent-radius-se:var(--radius-box);--tabcontent-radius-es:var(--radius-box);--tabcontent-radius-ee:var(--radius-box)}& :checked,& label:has(:checked),& :is(.tab-active,[aria-selected=true]){&+.tab-content{&:first-child,&:nth-child(n+3){--tabcontent-radius-ss:var(--radius-box)}}}}.sm\:tabs-top{--tabs-height:auto;--tabs-direction:row;& .tab{--tab-order:0;--tab-border:0 0 var(--border)0;--tab-radius-ss:min(var(--radius-field),var(--tab-radius-min));--tab-radius-se:min(var(--radius-field),var(--tab-radius-min));--tab-radius-es:0;--tab-radius-ee:0;--tab-paddings:var(--border)var(--tab-p)0 var(--tab-p);--tab-border-colors:#0000 #0000 var(--tab-border-color)#0000;--tab-corner-width:calc(100% + min(var(--radius-field),var(--tab-radius-min))*2);--tab-corner-height:min(var(--radius-field),var(--tab-radius-min));--tab-corner-position:top left,top right;&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked),&:is(label:has(:checked)){--tab-border:var(--border)var(--border)0 var(--border);--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color);--tab-paddings:0 calc(var(--tab-p) - var(--border))var(--border)calc(var(--tab-p) - var(--border));--tab-inset:auto auto 0 auto;--radius-start:radial-gradient(circle at top left,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));--radius-end:radial-gradient(circle at top right,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px))}}&:has(.tab-content){&>.tab:first-child{&:not(.tab-active,[aria-selected=true]){--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color)}}}& .tab-content{--tabcontent-order:1;--tabcontent-margin:calc(-1*var(--border))0 0 0;--tabcontent-radius-ss:0;--tabcontent-radius-se:var(--radius-box);--tabcontent-radius-es:var(--radius-box);--tabcontent-radius-ee:var(--radius-box)}& :checked,& label:has(:checked),& :is(.tab-active,[aria-selected=true]){&+.tab-content{&:first-child,&:nth-child(n+3){--tabcontent-radius-ss:var(--radius-box)}}}}.sm\:tabs-bottom{--tabs-height:auto;--tabs-direction:row;& .tab{--tab-order:1;--tab-border:var(--border)0 0 0;--tab-radius-ss:0;--tab-radius-se:0;--tab-radius-es:min(var(--radius-field),var(--tab-radius-min));--tab-radius-ee:min(var(--radius-field),var(--tab-radius-min));--tab-border-colors:var(--tab-border-color)#0000 #0000 #0000;--tab-paddings:0 var(--tab-p)var(--border)var(--tab-p);--tab-corner-width:calc(100% + min(var(--radius-field),var(--tab-radius-min))*2);--tab-corner-height:min(var(--radius-field),var(--tab-radius-min));--tab-corner-position:top left,top right;&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked),&:is(label:has(:checked)){--tab-border:0 var(--border)var(--border)var(--border);--tab-border-colors:#0000 var(--tab-border-color)var(--tab-border-color)var(--tab-border-color);--tab-paddings:var(--border)calc(var(--tab-p) - var(--border))0 calc(var(--tab-p) - var(--border));--tab-inset:0 auto auto auto;--radius-start:radial-gradient(circle at bottom left,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));--radius-end:radial-gradient(circle at bottom right,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px))}}&:has(.tab-content){&>.tab:first-child{&:not(.tab-active,[aria-selected=true]){--tab-border-colors:#0000 var(--tab-border-color)var(--tab-border-color)var(--tab-border-color)}}}& .tab-content{--tabcontent-order:0;--tabcontent-margin:0 0 calc(-1*var(--border))0;--tabcontent-radius-ss:var(--radius-box);--tabcontent-radius-se:var(--radius-box);--tabcontent-radius-es:0;--tabcontent-radius-ee:var(--radius-box)}&>:checked,&>:is(label:has(:checked)),&>:is(.tab-active,[aria-selected=true]){&+.tab-content:not(:nth-child(2)){--tabcontent-radius-es:var(--radius-box)}}}.sm\:tabs-box{background-color:var(--color-base-200);--tabs-box-radius:calc(var(--radius-field) + var(--radius-field) + var(--radius-field));border-radius:calc(var(--radius-field) + min(.25rem,var(--tabs-box-radius)));box-shadow:0 -.5px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 .5px oklch(0% 0 0/calc(var(--depth)*.05))inset;padding:.25rem;& .tab{border-radius:var(--radius-field);border-style:none;&:focus-visible,&:is(label:has(:checked:focus-visible)){outline-offset:2px}}&>:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&>:is(input:checked),&>:is(label:has(:checked)){background-color:var(--tab-bg,var(--color-base-100));box-shadow:0 1px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px 1px -1px color-mix(in oklab,var(--color-neutral)calc(var(--depth)*50%),#0000),0 1px 6px -4px color-mix(in oklab,var(--color-neutral)calc(var(--depth)*100%),#0000);@media (forced-colors:active){&{border:1px solid}}}}.sm\:tab-content{--tabcontent-radius-ss:0;--tabcontent-radius-se:0;--tabcontent-radius-es:0;--tabcontent-radius-ee:0;--tabcontent-order:1;width:100%;margin:var(--tabcontent-margin);order:1;order:var(--tabcontent-order);border-color:#0000;border-width:var(--border);border-start-start-radius:var(--tabcontent-radius-ss);border-start-end-radius:var(--tabcontent-radius-se);border-end-end-radius:var(--tabcontent-radius-ee);border-end-start-radius:var(--tabcontent-radius-es);display:none}.sm\:tabs-xs{--tab-height:calc(var(--size-field,.25rem)*6);& :where(.tab){--tab-p:.375rem;--tab-radius-min:calc(.5rem - var(--border));font-size:.75rem}}.sm\:tabs-sm{--tab-height:calc(var(--size-field,.25rem)*8);& :where(.tab){--tab-p:.5rem;--tab-radius-min:calc(.5rem - var(--border));font-size:.875rem}}.sm\:tabs-md{--tab-height:calc(var(--size-field,.25rem)*10);& :where(.tab){--tab-p:.75rem;--tab-radius-min:calc(.75rem - var(--border));font-size:.875rem}}.sm\:tabs-lg{--tab-height:calc(var(--size-field,.25rem)*12);& :where(.tab){--tab-p:1rem;--tab-radius-min:calc(1.5rem - var(--border));font-size:1.125rem}}.sm\:tabs-xl{--tab-height:calc(var(--size-field,.25rem)*14);& :where(.tab){--tab-p:1.25rem;--tab-radius-min:calc(2rem - var(--border));font-size:1.125rem}}}@media (width>=768px){.md\:tabs{--tabs-height:auto;--tabs-direction:row;--tab-height:calc(var(--size-field,.25rem)*10);height:var(--tabs-height);flex-wrap:wrap;flex-direction:var(--tabs-direction);display:flex}.md\:tab{cursor:pointer;appearance:none;text-align:center;-webkit-user-select:none;user-select:none;--tab-p:1rem;--tab-bg:var(--color-base-100);--tab-border-color:var(--color-base-300);--tab-radius-ss:0;--tab-radius-se:0;--tab-radius-es:0;--tab-radius-ee:0;--tab-order:0;--tab-radius-min:calc(.75rem - var(--border));flex-wrap:wrap;order:var(--tab-order);height:var(--tab-height);border-color:#0000;justify-content:center;align-items:center;padding-inline-start:var(--tab-p);padding-inline-end:var(--tab-p);font-size:.875rem;display:inline-flex;position:relative;&:hover{@media (hover:hover){&{color:var(--color-base-content)}}}&:is(input[type=radio]){min-width:fit-content;&:after{content:attr(aria-label)}}&:is(label){position:relative;& input{cursor:pointer;appearance:none;opacity:0;position:absolute;inset:0}}&:checked,&:is(label:has(:checked)),&:is(.tab-active,[aria-selected=true]){&+.tab-content{height:calc(100% - var(--tab-height) + var(--border));display:block}}&:not(:checked,label:has(:checked),:hover,.tab-active,[aria-selected=true]){color:color-mix(in oklab,var(--color-base-content)50%,transparent)}&:not(input):empty{cursor:default;flex-grow:1}&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible,&:is(label:has(:checked:focus-visible)){outline-offset:-5px;outline:2px solid}&[disabled]{pointer-events:none;opacity:.4}}.md\:tab-disabled{pointer-events:none;opacity:.4}.md\:tabs-border{& .tab{--tab-border-color:#0000 #0000 var(--tab-border-color)#0000;border-radius:var(--radius-field);position:relative;&:before{--tw-content:"";content:var(--tw-content);background-color:var(--tab-border-color);border-radius:var(--radius-field);width:80%;height:3px;transition:background-color .2s;position:absolute;bottom:0;left:10%}&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked),&:is(label:has(:checked)){&:before{--tab-border-color:currentColor;border-top:3px solid}}}}.md\:tabs-lift{--tabs-height:auto;--tabs-direction:row;&>.tab{--tab-border:0 0 var(--border)0;--tab-radius-ss:min(var(--radius-field),var(--tab-radius-min));--tab-radius-se:min(var(--radius-field),var(--tab-radius-min));--tab-radius-es:0;--tab-radius-ee:0;--tab-paddings:var(--border)var(--tab-p)0 var(--tab-p);--tab-border-colors:#0000 #0000 var(--tab-border-color)#0000;--tab-corner-width:calc(100% + min(var(--radius-field),var(--tab-radius-min))*2);--tab-corner-height:min(var(--radius-field),var(--tab-radius-min));--tab-corner-position:top left,top right;border-width:var(--tab-border);padding:var(--tab-paddings);border-color:var(--tab-border-colors);border-start-start-radius:var(--tab-radius-ss);border-start-end-radius:var(--tab-radius-se);border-end-end-radius:var(--tab-radius-ee);border-end-start-radius:var(--tab-radius-es);&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked,label:has(:checked)){--tab-border:var(--border)var(--border)0 var(--border);--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color);--tab-paddings:0 calc(var(--tab-p) - var(--border))var(--border)calc(var(--tab-p) - var(--border));--tab-inset:auto auto 0 auto;--tab-grad:calc(69% - var(--border));--radius-start:radial-gradient(circle at top left,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));--radius-end:radial-gradient(circle at top right,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));background-color:var(--tab-bg);&:before{z-index:1;content:"";width:var(--tab-corner-width);height:var(--tab-corner-height);background-position:var(--tab-corner-position);background-image:var(--radius-start),var(--radius-end);background-size:min(var(--radius-field),var(--tab-radius-min))min(var(--radius-field),var(--tab-radius-min));inset:var(--tab-inset);background-repeat:no-repeat;display:block;position:absolute}&:first-child:before{--radius-start:none}[dir=rtl] &:first-child:before{transform:rotateY(180deg)}&:last-child:before{--radius-end:none}[dir=rtl] &:last-child:before{transform:rotateY(180deg)}}}&:has(.tab-content){&>.tab:first-child{&:not(.tab-active,[aria-selected=true]){--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color)}}}& .tab-content{--tabcontent-margin:calc(-1*var(--border))0 0 0;--tabcontent-radius-ss:0;--tabcontent-radius-se:var(--radius-box);--tabcontent-radius-es:var(--radius-box);--tabcontent-radius-ee:var(--radius-box)}& :checked,& label:has(:checked),& :is(.tab-active,[aria-selected=true]){&+.tab-content{&:first-child,&:nth-child(n+3){--tabcontent-radius-ss:var(--radius-box)}}}}.md\:tabs-top{--tabs-height:auto;--tabs-direction:row;& .tab{--tab-order:0;--tab-border:0 0 var(--border)0;--tab-radius-ss:min(var(--radius-field),var(--tab-radius-min));--tab-radius-se:min(var(--radius-field),var(--tab-radius-min));--tab-radius-es:0;--tab-radius-ee:0;--tab-paddings:var(--border)var(--tab-p)0 var(--tab-p);--tab-border-colors:#0000 #0000 var(--tab-border-color)#0000;--tab-corner-width:calc(100% + min(var(--radius-field),var(--tab-radius-min))*2);--tab-corner-height:min(var(--radius-field),var(--tab-radius-min));--tab-corner-position:top left,top right;&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked),&:is(label:has(:checked)){--tab-border:var(--border)var(--border)0 var(--border);--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color);--tab-paddings:0 calc(var(--tab-p) - var(--border))var(--border)calc(var(--tab-p) - var(--border));--tab-inset:auto auto 0 auto;--radius-start:radial-gradient(circle at top left,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));--radius-end:radial-gradient(circle at top right,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px))}}&:has(.tab-content){&>.tab:first-child{&:not(.tab-active,[aria-selected=true]){--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color)}}}& .tab-content{--tabcontent-order:1;--tabcontent-margin:calc(-1*var(--border))0 0 0;--tabcontent-radius-ss:0;--tabcontent-radius-se:var(--radius-box);--tabcontent-radius-es:var(--radius-box);--tabcontent-radius-ee:var(--radius-box)}& :checked,& label:has(:checked),& :is(.tab-active,[aria-selected=true]){&+.tab-content{&:first-child,&:nth-child(n+3){--tabcontent-radius-ss:var(--radius-box)}}}}.md\:tabs-bottom{--tabs-height:auto;--tabs-direction:row;& .tab{--tab-order:1;--tab-border:var(--border)0 0 0;--tab-radius-ss:0;--tab-radius-se:0;--tab-radius-es:min(var(--radius-field),var(--tab-radius-min));--tab-radius-ee:min(var(--radius-field),var(--tab-radius-min));--tab-border-colors:var(--tab-border-color)#0000 #0000 #0000;--tab-paddings:0 var(--tab-p)var(--border)var(--tab-p);--tab-corner-width:calc(100% + min(var(--radius-field),var(--tab-radius-min))*2);--tab-corner-height:min(var(--radius-field),var(--tab-radius-min));--tab-corner-position:top left,top right;&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked),&:is(label:has(:checked)){--tab-border:0 var(--border)var(--border)var(--border);--tab-border-colors:#0000 var(--tab-border-color)var(--tab-border-color)var(--tab-border-color);--tab-paddings:var(--border)calc(var(--tab-p) - var(--border))0 calc(var(--tab-p) - var(--border));--tab-inset:0 auto auto auto;--radius-start:radial-gradient(circle at bottom left,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));--radius-end:radial-gradient(circle at bottom right,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px))}}&:has(.tab-content){&>.tab:first-child{&:not(.tab-active,[aria-selected=true]){--tab-border-colors:#0000 var(--tab-border-color)var(--tab-border-color)var(--tab-border-color)}}}& .tab-content{--tabcontent-order:0;--tabcontent-margin:0 0 calc(-1*var(--border))0;--tabcontent-radius-ss:var(--radius-box);--tabcontent-radius-se:var(--radius-box);--tabcontent-radius-es:0;--tabcontent-radius-ee:var(--radius-box)}&>:checked,&>:is(label:has(:checked)),&>:is(.tab-active,[aria-selected=true]){&+.tab-content:not(:nth-child(2)){--tabcontent-radius-es:var(--radius-box)}}}.md\:tabs-box{background-color:var(--color-base-200);--tabs-box-radius:calc(var(--radius-field) + var(--radius-field) + var(--radius-field));border-radius:calc(var(--radius-field) + min(.25rem,var(--tabs-box-radius)));box-shadow:0 -.5px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 .5px oklch(0% 0 0/calc(var(--depth)*.05))inset;padding:.25rem;& .tab{border-radius:var(--radius-field);border-style:none;&:focus-visible,&:is(label:has(:checked:focus-visible)){outline-offset:2px}}&>:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&>:is(input:checked),&>:is(label:has(:checked)){background-color:var(--tab-bg,var(--color-base-100));box-shadow:0 1px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px 1px -1px color-mix(in oklab,var(--color-neutral)calc(var(--depth)*50%),#0000),0 1px 6px -4px color-mix(in oklab,var(--color-neutral)calc(var(--depth)*100%),#0000);@media (forced-colors:active){&{border:1px solid}}}}.md\:tab-content{--tabcontent-radius-ss:0;--tabcontent-radius-se:0;--tabcontent-radius-es:0;--tabcontent-radius-ee:0;--tabcontent-order:1;width:100%;margin:var(--tabcontent-margin);order:1;order:var(--tabcontent-order);border-color:#0000;border-width:var(--border);border-start-start-radius:var(--tabcontent-radius-ss);border-start-end-radius:var(--tabcontent-radius-se);border-end-end-radius:var(--tabcontent-radius-ee);border-end-start-radius:var(--tabcontent-radius-es);display:none}.md\:tabs-xs{--tab-height:calc(var(--size-field,.25rem)*6);& :where(.tab){--tab-p:.375rem;--tab-radius-min:calc(.5rem - var(--border));font-size:.75rem}}.md\:tabs-sm{--tab-height:calc(var(--size-field,.25rem)*8);& :where(.tab){--tab-p:.5rem;--tab-radius-min:calc(.5rem - var(--border));font-size:.875rem}}.md\:tabs-md{--tab-height:calc(var(--size-field,.25rem)*10);& :where(.tab){--tab-p:.75rem;--tab-radius-min:calc(.75rem - var(--border));font-size:.875rem}}.md\:tabs-lg{--tab-height:calc(var(--size-field,.25rem)*12);& :where(.tab){--tab-p:1rem;--tab-radius-min:calc(1.5rem - var(--border));font-size:1.125rem}}.md\:tabs-xl{--tab-height:calc(var(--size-field,.25rem)*14);& :where(.tab){--tab-p:1.25rem;--tab-radius-min:calc(2rem - var(--border));font-size:1.125rem}}}@media (width>=1024px){.lg\:tabs{--tabs-height:auto;--tabs-direction:row;--tab-height:calc(var(--size-field,.25rem)*10);height:var(--tabs-height);flex-wrap:wrap;flex-direction:var(--tabs-direction);display:flex}.lg\:tab{cursor:pointer;appearance:none;text-align:center;-webkit-user-select:none;user-select:none;--tab-p:1rem;--tab-bg:var(--color-base-100);--tab-border-color:var(--color-base-300);--tab-radius-ss:0;--tab-radius-se:0;--tab-radius-es:0;--tab-radius-ee:0;--tab-order:0;--tab-radius-min:calc(.75rem - var(--border));flex-wrap:wrap;order:var(--tab-order);height:var(--tab-height);border-color:#0000;justify-content:center;align-items:center;padding-inline-start:var(--tab-p);padding-inline-end:var(--tab-p);font-size:.875rem;display:inline-flex;position:relative;&:hover{@media (hover:hover){&{color:var(--color-base-content)}}}&:is(input[type=radio]){min-width:fit-content;&:after{content:attr(aria-label)}}&:is(label){position:relative;& input{cursor:pointer;appearance:none;opacity:0;position:absolute;inset:0}}&:checked,&:is(label:has(:checked)),&:is(.tab-active,[aria-selected=true]){&+.tab-content{height:calc(100% - var(--tab-height) + var(--border));display:block}}&:not(:checked,label:has(:checked),:hover,.tab-active,[aria-selected=true]){color:color-mix(in oklab,var(--color-base-content)50%,transparent)}&:not(input):empty{cursor:default;flex-grow:1}&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible,&:is(label:has(:checked:focus-visible)){outline-offset:-5px;outline:2px solid}&[disabled]{pointer-events:none;opacity:.4}}.lg\:tab-disabled{pointer-events:none;opacity:.4}.lg\:tabs-border{& .tab{--tab-border-color:#0000 #0000 var(--tab-border-color)#0000;border-radius:var(--radius-field);position:relative;&:before{--tw-content:"";content:var(--tw-content);background-color:var(--tab-border-color);border-radius:var(--radius-field);width:80%;height:3px;transition:background-color .2s;position:absolute;bottom:0;left:10%}&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked),&:is(label:has(:checked)){&:before{--tab-border-color:currentColor;border-top:3px solid}}}}.lg\:tabs-lift{--tabs-height:auto;--tabs-direction:row;&>.tab{--tab-border:0 0 var(--border)0;--tab-radius-ss:min(var(--radius-field),var(--tab-radius-min));--tab-radius-se:min(var(--radius-field),var(--tab-radius-min));--tab-radius-es:0;--tab-radius-ee:0;--tab-paddings:var(--border)var(--tab-p)0 var(--tab-p);--tab-border-colors:#0000 #0000 var(--tab-border-color)#0000;--tab-corner-width:calc(100% + min(var(--radius-field),var(--tab-radius-min))*2);--tab-corner-height:min(var(--radius-field),var(--tab-radius-min));--tab-corner-position:top left,top right;border-width:var(--tab-border);padding:var(--tab-paddings);border-color:var(--tab-border-colors);border-start-start-radius:var(--tab-radius-ss);border-start-end-radius:var(--tab-radius-se);border-end-end-radius:var(--tab-radius-ee);border-end-start-radius:var(--tab-radius-es);&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked,label:has(:checked)){--tab-border:var(--border)var(--border)0 var(--border);--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color);--tab-paddings:0 calc(var(--tab-p) - var(--border))var(--border)calc(var(--tab-p) - var(--border));--tab-inset:auto auto 0 auto;--tab-grad:calc(69% - var(--border));--radius-start:radial-gradient(circle at top left,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));--radius-end:radial-gradient(circle at top right,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));background-color:var(--tab-bg);&:before{z-index:1;content:"";width:var(--tab-corner-width);height:var(--tab-corner-height);background-position:var(--tab-corner-position);background-image:var(--radius-start),var(--radius-end);background-size:min(var(--radius-field),var(--tab-radius-min))min(var(--radius-field),var(--tab-radius-min));inset:var(--tab-inset);background-repeat:no-repeat;display:block;position:absolute}&:first-child:before{--radius-start:none}[dir=rtl] &:first-child:before{transform:rotateY(180deg)}&:last-child:before{--radius-end:none}[dir=rtl] &:last-child:before{transform:rotateY(180deg)}}}&:has(.tab-content){&>.tab:first-child{&:not(.tab-active,[aria-selected=true]){--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color)}}}& .tab-content{--tabcontent-margin:calc(-1*var(--border))0 0 0;--tabcontent-radius-ss:0;--tabcontent-radius-se:var(--radius-box);--tabcontent-radius-es:var(--radius-box);--tabcontent-radius-ee:var(--radius-box)}& :checked,& label:has(:checked),& :is(.tab-active,[aria-selected=true]){&+.tab-content{&:first-child,&:nth-child(n+3){--tabcontent-radius-ss:var(--radius-box)}}}}.lg\:tabs-top{--tabs-height:auto;--tabs-direction:row;& .tab{--tab-order:0;--tab-border:0 0 var(--border)0;--tab-radius-ss:min(var(--radius-field),var(--tab-radius-min));--tab-radius-se:min(var(--radius-field),var(--tab-radius-min));--tab-radius-es:0;--tab-radius-ee:0;--tab-paddings:var(--border)var(--tab-p)0 var(--tab-p);--tab-border-colors:#0000 #0000 var(--tab-border-color)#0000;--tab-corner-width:calc(100% + min(var(--radius-field),var(--tab-radius-min))*2);--tab-corner-height:min(var(--radius-field),var(--tab-radius-min));--tab-corner-position:top left,top right;&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked),&:is(label:has(:checked)){--tab-border:var(--border)var(--border)0 var(--border);--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color);--tab-paddings:0 calc(var(--tab-p) - var(--border))var(--border)calc(var(--tab-p) - var(--border));--tab-inset:auto auto 0 auto;--radius-start:radial-gradient(circle at top left,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));--radius-end:radial-gradient(circle at top right,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px))}}&:has(.tab-content){&>.tab:first-child{&:not(.tab-active,[aria-selected=true]){--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color)}}}& .tab-content{--tabcontent-order:1;--tabcontent-margin:calc(-1*var(--border))0 0 0;--tabcontent-radius-ss:0;--tabcontent-radius-se:var(--radius-box);--tabcontent-radius-es:var(--radius-box);--tabcontent-radius-ee:var(--radius-box)}& :checked,& label:has(:checked),& :is(.tab-active,[aria-selected=true]){&+.tab-content{&:first-child,&:nth-child(n+3){--tabcontent-radius-ss:var(--radius-box)}}}}.lg\:tabs-bottom{--tabs-height:auto;--tabs-direction:row;& .tab{--tab-order:1;--tab-border:var(--border)0 0 0;--tab-radius-ss:0;--tab-radius-se:0;--tab-radius-es:min(var(--radius-field),var(--tab-radius-min));--tab-radius-ee:min(var(--radius-field),var(--tab-radius-min));--tab-border-colors:var(--tab-border-color)#0000 #0000 #0000;--tab-paddings:0 var(--tab-p)var(--border)var(--tab-p);--tab-corner-width:calc(100% + min(var(--radius-field),var(--tab-radius-min))*2);--tab-corner-height:min(var(--radius-field),var(--tab-radius-min));--tab-corner-position:top left,top right;&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked),&:is(label:has(:checked)){--tab-border:0 var(--border)var(--border)var(--border);--tab-border-colors:#0000 var(--tab-border-color)var(--tab-border-color)var(--tab-border-color);--tab-paddings:var(--border)calc(var(--tab-p) - var(--border))0 calc(var(--tab-p) - var(--border));--tab-inset:0 auto auto auto;--radius-start:radial-gradient(circle at bottom left,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));--radius-end:radial-gradient(circle at bottom right,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px))}}&:has(.tab-content){&>.tab:first-child{&:not(.tab-active,[aria-selected=true]){--tab-border-colors:#0000 var(--tab-border-color)var(--tab-border-color)var(--tab-border-color)}}}& .tab-content{--tabcontent-order:0;--tabcontent-margin:0 0 calc(-1*var(--border))0;--tabcontent-radius-ss:var(--radius-box);--tabcontent-radius-se:var(--radius-box);--tabcontent-radius-es:0;--tabcontent-radius-ee:var(--radius-box)}&>:checked,&>:is(label:has(:checked)),&>:is(.tab-active,[aria-selected=true]){&+.tab-content:not(:nth-child(2)){--tabcontent-radius-es:var(--radius-box)}}}.lg\:tabs-box{background-color:var(--color-base-200);--tabs-box-radius:calc(var(--radius-field) + var(--radius-field) + var(--radius-field));border-radius:calc(var(--radius-field) + min(.25rem,var(--tabs-box-radius)));box-shadow:0 -.5px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 .5px oklch(0% 0 0/calc(var(--depth)*.05))inset;padding:.25rem;& .tab{border-radius:var(--radius-field);border-style:none;&:focus-visible,&:is(label:has(:checked:focus-visible)){outline-offset:2px}}&>:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&>:is(input:checked),&>:is(label:has(:checked)){background-color:var(--tab-bg,var(--color-base-100));box-shadow:0 1px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px 1px -1px color-mix(in oklab,var(--color-neutral)calc(var(--depth)*50%),#0000),0 1px 6px -4px color-mix(in oklab,var(--color-neutral)calc(var(--depth)*100%),#0000);@media (forced-colors:active){&{border:1px solid}}}}.lg\:tab-content{--tabcontent-radius-ss:0;--tabcontent-radius-se:0;--tabcontent-radius-es:0;--tabcontent-radius-ee:0;--tabcontent-order:1;width:100%;margin:var(--tabcontent-margin);order:1;order:var(--tabcontent-order);border-color:#0000;border-width:var(--border);border-start-start-radius:var(--tabcontent-radius-ss);border-start-end-radius:var(--tabcontent-radius-se);border-end-end-radius:var(--tabcontent-radius-ee);border-end-start-radius:var(--tabcontent-radius-es);display:none}.lg\:tabs-xs{--tab-height:calc(var(--size-field,.25rem)*6);& :where(.tab){--tab-p:.375rem;--tab-radius-min:calc(.5rem - var(--border));font-size:.75rem}}.lg\:tabs-sm{--tab-height:calc(var(--size-field,.25rem)*8);& :where(.tab){--tab-p:.5rem;--tab-radius-min:calc(.5rem - var(--border));font-size:.875rem}}.lg\:tabs-md{--tab-height:calc(var(--size-field,.25rem)*10);& :where(.tab){--tab-p:.75rem;--tab-radius-min:calc(.75rem - var(--border));font-size:.875rem}}.lg\:tabs-lg{--tab-height:calc(var(--size-field,.25rem)*12);& :where(.tab){--tab-p:1rem;--tab-radius-min:calc(1.5rem - var(--border));font-size:1.125rem}}.lg\:tabs-xl{--tab-height:calc(var(--size-field,.25rem)*14);& :where(.tab){--tab-p:1.25rem;--tab-radius-min:calc(2rem - var(--border));font-size:1.125rem}}}@media (width>=1280px){.xl\:tabs{--tabs-height:auto;--tabs-direction:row;--tab-height:calc(var(--size-field,.25rem)*10);height:var(--tabs-height);flex-wrap:wrap;flex-direction:var(--tabs-direction);display:flex}.xl\:tab{cursor:pointer;appearance:none;text-align:center;-webkit-user-select:none;user-select:none;--tab-p:1rem;--tab-bg:var(--color-base-100);--tab-border-color:var(--color-base-300);--tab-radius-ss:0;--tab-radius-se:0;--tab-radius-es:0;--tab-radius-ee:0;--tab-order:0;--tab-radius-min:calc(.75rem - var(--border));flex-wrap:wrap;order:var(--tab-order);height:var(--tab-height);border-color:#0000;justify-content:center;align-items:center;padding-inline-start:var(--tab-p);padding-inline-end:var(--tab-p);font-size:.875rem;display:inline-flex;position:relative;&:hover{@media (hover:hover){&{color:var(--color-base-content)}}}&:is(input[type=radio]){min-width:fit-content;&:after{content:attr(aria-label)}}&:is(label){position:relative;& input{cursor:pointer;appearance:none;opacity:0;position:absolute;inset:0}}&:checked,&:is(label:has(:checked)),&:is(.tab-active,[aria-selected=true]){&+.tab-content{height:calc(100% - var(--tab-height) + var(--border));display:block}}&:not(:checked,label:has(:checked),:hover,.tab-active,[aria-selected=true]){color:color-mix(in oklab,var(--color-base-content)50%,transparent)}&:not(input):empty{cursor:default;flex-grow:1}&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible,&:is(label:has(:checked:focus-visible)){outline-offset:-5px;outline:2px solid}&[disabled]{pointer-events:none;opacity:.4}}.xl\:tab-disabled{pointer-events:none;opacity:.4}.xl\:tabs-border{& .tab{--tab-border-color:#0000 #0000 var(--tab-border-color)#0000;border-radius:var(--radius-field);position:relative;&:before{--tw-content:"";content:var(--tw-content);background-color:var(--tab-border-color);border-radius:var(--radius-field);width:80%;height:3px;transition:background-color .2s;position:absolute;bottom:0;left:10%}&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked),&:is(label:has(:checked)){&:before{--tab-border-color:currentColor;border-top:3px solid}}}}.xl\:tabs-lift{--tabs-height:auto;--tabs-direction:row;&>.tab{--tab-border:0 0 var(--border)0;--tab-radius-ss:min(var(--radius-field),var(--tab-radius-min));--tab-radius-se:min(var(--radius-field),var(--tab-radius-min));--tab-radius-es:0;--tab-radius-ee:0;--tab-paddings:var(--border)var(--tab-p)0 var(--tab-p);--tab-border-colors:#0000 #0000 var(--tab-border-color)#0000;--tab-corner-width:calc(100% + min(var(--radius-field),var(--tab-radius-min))*2);--tab-corner-height:min(var(--radius-field),var(--tab-radius-min));--tab-corner-position:top left,top right;border-width:var(--tab-border);padding:var(--tab-paddings);border-color:var(--tab-border-colors);border-start-start-radius:var(--tab-radius-ss);border-start-end-radius:var(--tab-radius-se);border-end-end-radius:var(--tab-radius-ee);border-end-start-radius:var(--tab-radius-es);&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked,label:has(:checked)){--tab-border:var(--border)var(--border)0 var(--border);--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color);--tab-paddings:0 calc(var(--tab-p) - var(--border))var(--border)calc(var(--tab-p) - var(--border));--tab-inset:auto auto 0 auto;--tab-grad:calc(69% - var(--border));--radius-start:radial-gradient(circle at top left,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));--radius-end:radial-gradient(circle at top right,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));background-color:var(--tab-bg);&:before{z-index:1;content:"";width:var(--tab-corner-width);height:var(--tab-corner-height);background-position:var(--tab-corner-position);background-image:var(--radius-start),var(--radius-end);background-size:min(var(--radius-field),var(--tab-radius-min))min(var(--radius-field),var(--tab-radius-min));inset:var(--tab-inset);background-repeat:no-repeat;display:block;position:absolute}&:first-child:before{--radius-start:none}[dir=rtl] &:first-child:before{transform:rotateY(180deg)}&:last-child:before{--radius-end:none}[dir=rtl] &:last-child:before{transform:rotateY(180deg)}}}&:has(.tab-content){&>.tab:first-child{&:not(.tab-active,[aria-selected=true]){--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color)}}}& .tab-content{--tabcontent-margin:calc(-1*var(--border))0 0 0;--tabcontent-radius-ss:0;--tabcontent-radius-se:var(--radius-box);--tabcontent-radius-es:var(--radius-box);--tabcontent-radius-ee:var(--radius-box)}& :checked,& label:has(:checked),& :is(.tab-active,[aria-selected=true]){&+.tab-content{&:first-child,&:nth-child(n+3){--tabcontent-radius-ss:var(--radius-box)}}}}.xl\:tabs-top{--tabs-height:auto;--tabs-direction:row;& .tab{--tab-order:0;--tab-border:0 0 var(--border)0;--tab-radius-ss:min(var(--radius-field),var(--tab-radius-min));--tab-radius-se:min(var(--radius-field),var(--tab-radius-min));--tab-radius-es:0;--tab-radius-ee:0;--tab-paddings:var(--border)var(--tab-p)0 var(--tab-p);--tab-border-colors:#0000 #0000 var(--tab-border-color)#0000;--tab-corner-width:calc(100% + min(var(--radius-field),var(--tab-radius-min))*2);--tab-corner-height:min(var(--radius-field),var(--tab-radius-min));--tab-corner-position:top left,top right;&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked),&:is(label:has(:checked)){--tab-border:var(--border)var(--border)0 var(--border);--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color);--tab-paddings:0 calc(var(--tab-p) - var(--border))var(--border)calc(var(--tab-p) - var(--border));--tab-inset:auto auto 0 auto;--radius-start:radial-gradient(circle at top left,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));--radius-end:radial-gradient(circle at top right,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px))}}&:has(.tab-content){&>.tab:first-child{&:not(.tab-active,[aria-selected=true]){--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color)}}}& .tab-content{--tabcontent-order:1;--tabcontent-margin:calc(-1*var(--border))0 0 0;--tabcontent-radius-ss:0;--tabcontent-radius-se:var(--radius-box);--tabcontent-radius-es:var(--radius-box);--tabcontent-radius-ee:var(--radius-box)}& :checked,& label:has(:checked),& :is(.tab-active,[aria-selected=true]){&+.tab-content{&:first-child,&:nth-child(n+3){--tabcontent-radius-ss:var(--radius-box)}}}}.xl\:tabs-bottom{--tabs-height:auto;--tabs-direction:row;& .tab{--tab-order:1;--tab-border:var(--border)0 0 0;--tab-radius-ss:0;--tab-radius-se:0;--tab-radius-es:min(var(--radius-field),var(--tab-radius-min));--tab-radius-ee:min(var(--radius-field),var(--tab-radius-min));--tab-border-colors:var(--tab-border-color)#0000 #0000 #0000;--tab-paddings:0 var(--tab-p)var(--border)var(--tab-p);--tab-corner-width:calc(100% + min(var(--radius-field),var(--tab-radius-min))*2);--tab-corner-height:min(var(--radius-field),var(--tab-radius-min));--tab-corner-position:top left,top right;&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked),&:is(label:has(:checked)){--tab-border:0 var(--border)var(--border)var(--border);--tab-border-colors:#0000 var(--tab-border-color)var(--tab-border-color)var(--tab-border-color);--tab-paddings:var(--border)calc(var(--tab-p) - var(--border))0 calc(var(--tab-p) - var(--border));--tab-inset:0 auto auto auto;--radius-start:radial-gradient(circle at bottom left,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));--radius-end:radial-gradient(circle at bottom right,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px))}}&:has(.tab-content){&>.tab:first-child{&:not(.tab-active,[aria-selected=true]){--tab-border-colors:#0000 var(--tab-border-color)var(--tab-border-color)var(--tab-border-color)}}}& .tab-content{--tabcontent-order:0;--tabcontent-margin:0 0 calc(-1*var(--border))0;--tabcontent-radius-ss:var(--radius-box);--tabcontent-radius-se:var(--radius-box);--tabcontent-radius-es:0;--tabcontent-radius-ee:var(--radius-box)}&>:checked,&>:is(label:has(:checked)),&>:is(.tab-active,[aria-selected=true]){&+.tab-content:not(:nth-child(2)){--tabcontent-radius-es:var(--radius-box)}}}.xl\:tabs-box{background-color:var(--color-base-200);--tabs-box-radius:calc(var(--radius-field) + var(--radius-field) + var(--radius-field));border-radius:calc(var(--radius-field) + min(.25rem,var(--tabs-box-radius)));box-shadow:0 -.5px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 .5px oklch(0% 0 0/calc(var(--depth)*.05))inset;padding:.25rem;& .tab{border-radius:var(--radius-field);border-style:none;&:focus-visible,&:is(label:has(:checked:focus-visible)){outline-offset:2px}}&>:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&>:is(input:checked),&>:is(label:has(:checked)){background-color:var(--tab-bg,var(--color-base-100));box-shadow:0 1px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px 1px -1px color-mix(in oklab,var(--color-neutral)calc(var(--depth)*50%),#0000),0 1px 6px -4px color-mix(in oklab,var(--color-neutral)calc(var(--depth)*100%),#0000);@media (forced-colors:active){&{border:1px solid}}}}.xl\:tab-content{--tabcontent-radius-ss:0;--tabcontent-radius-se:0;--tabcontent-radius-es:0;--tabcontent-radius-ee:0;--tabcontent-order:1;width:100%;margin:var(--tabcontent-margin);order:1;order:var(--tabcontent-order);border-color:#0000;border-width:var(--border);border-start-start-radius:var(--tabcontent-radius-ss);border-start-end-radius:var(--tabcontent-radius-se);border-end-end-radius:var(--tabcontent-radius-ee);border-end-start-radius:var(--tabcontent-radius-es);display:none}.xl\:tabs-xs{--tab-height:calc(var(--size-field,.25rem)*6);& :where(.tab){--tab-p:.375rem;--tab-radius-min:calc(.5rem - var(--border));font-size:.75rem}}.xl\:tabs-sm{--tab-height:calc(var(--size-field,.25rem)*8);& :where(.tab){--tab-p:.5rem;--tab-radius-min:calc(.5rem - var(--border));font-size:.875rem}}.xl\:tabs-md{--tab-height:calc(var(--size-field,.25rem)*10);& :where(.tab){--tab-p:.75rem;--tab-radius-min:calc(.75rem - var(--border));font-size:.875rem}}.xl\:tabs-lg{--tab-height:calc(var(--size-field,.25rem)*12);& :where(.tab){--tab-p:1rem;--tab-radius-min:calc(1.5rem - var(--border));font-size:1.125rem}}.xl\:tabs-xl{--tab-height:calc(var(--size-field,.25rem)*14);& :where(.tab){--tab-p:1.25rem;--tab-radius-min:calc(2rem - var(--border));font-size:1.125rem}}}@media (width>=1536px){.\32 xl\:tabs{--tabs-height:auto;--tabs-direction:row;--tab-height:calc(var(--size-field,.25rem)*10);height:var(--tabs-height);flex-wrap:wrap;flex-direction:var(--tabs-direction);display:flex}.\32 xl\:tab{cursor:pointer;appearance:none;text-align:center;-webkit-user-select:none;user-select:none;--tab-p:1rem;--tab-bg:var(--color-base-100);--tab-border-color:var(--color-base-300);--tab-radius-ss:0;--tab-radius-se:0;--tab-radius-es:0;--tab-radius-ee:0;--tab-order:0;--tab-radius-min:calc(.75rem - var(--border));flex-wrap:wrap;order:var(--tab-order);height:var(--tab-height);border-color:#0000;justify-content:center;align-items:center;padding-inline-start:var(--tab-p);padding-inline-end:var(--tab-p);font-size:.875rem;display:inline-flex;position:relative;&:hover{@media (hover:hover){&{color:var(--color-base-content)}}}&:is(input[type=radio]){min-width:fit-content;&:after{content:attr(aria-label)}}&:is(label){position:relative;& input{cursor:pointer;appearance:none;opacity:0;position:absolute;inset:0}}&:checked,&:is(label:has(:checked)),&:is(.tab-active,[aria-selected=true]){&+.tab-content{height:calc(100% - var(--tab-height) + var(--border));display:block}}&:not(:checked,label:has(:checked),:hover,.tab-active,[aria-selected=true]){color:color-mix(in oklab,var(--color-base-content)50%,transparent)}&:not(input):empty{cursor:default;flex-grow:1}&:focus{--tw-outline-style:none;outline-style:none;@media (forced-colors:active){&{outline-offset:2px;outline:2px solid #0000}}}&:focus-visible,&:is(label:has(:checked:focus-visible)){outline-offset:-5px;outline:2px solid}&[disabled]{pointer-events:none;opacity:.4}}.\32 xl\:tab-disabled{pointer-events:none;opacity:.4}.\32 xl\:tabs-border{& .tab{--tab-border-color:#0000 #0000 var(--tab-border-color)#0000;border-radius:var(--radius-field);position:relative;&:before{--tw-content:"";content:var(--tw-content);background-color:var(--tab-border-color);border-radius:var(--radius-field);width:80%;height:3px;transition:background-color .2s;position:absolute;bottom:0;left:10%}&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked),&:is(label:has(:checked)){&:before{--tab-border-color:currentColor;border-top:3px solid}}}}.\32 xl\:tabs-lift{--tabs-height:auto;--tabs-direction:row;&>.tab{--tab-border:0 0 var(--border)0;--tab-radius-ss:min(var(--radius-field),var(--tab-radius-min));--tab-radius-se:min(var(--radius-field),var(--tab-radius-min));--tab-radius-es:0;--tab-radius-ee:0;--tab-paddings:var(--border)var(--tab-p)0 var(--tab-p);--tab-border-colors:#0000 #0000 var(--tab-border-color)#0000;--tab-corner-width:calc(100% + min(var(--radius-field),var(--tab-radius-min))*2);--tab-corner-height:min(var(--radius-field),var(--tab-radius-min));--tab-corner-position:top left,top right;border-width:var(--tab-border);padding:var(--tab-paddings);border-color:var(--tab-border-colors);border-start-start-radius:var(--tab-radius-ss);border-start-end-radius:var(--tab-radius-se);border-end-end-radius:var(--tab-radius-ee);border-end-start-radius:var(--tab-radius-es);&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked,label:has(:checked)){--tab-border:var(--border)var(--border)0 var(--border);--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color);--tab-paddings:0 calc(var(--tab-p) - var(--border))var(--border)calc(var(--tab-p) - var(--border));--tab-inset:auto auto 0 auto;--tab-grad:calc(69% - var(--border));--radius-start:radial-gradient(circle at top left,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));--radius-end:radial-gradient(circle at top right,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));background-color:var(--tab-bg);&:before{z-index:1;content:"";width:var(--tab-corner-width);height:var(--tab-corner-height);background-position:var(--tab-corner-position);background-image:var(--radius-start),var(--radius-end);background-size:min(var(--radius-field),var(--tab-radius-min))min(var(--radius-field),var(--tab-radius-min));inset:var(--tab-inset);background-repeat:no-repeat;display:block;position:absolute}&:first-child:before{--radius-start:none}[dir=rtl] &:first-child:before{transform:rotateY(180deg)}&:last-child:before{--radius-end:none}[dir=rtl] &:last-child:before{transform:rotateY(180deg)}}}&:has(.tab-content){&>.tab:first-child{&:not(.tab-active,[aria-selected=true]){--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color)}}}& .tab-content{--tabcontent-margin:calc(-1*var(--border))0 0 0;--tabcontent-radius-ss:0;--tabcontent-radius-se:var(--radius-box);--tabcontent-radius-es:var(--radius-box);--tabcontent-radius-ee:var(--radius-box)}& :checked,& label:has(:checked),& :is(.tab-active,[aria-selected=true]){&+.tab-content{&:first-child,&:nth-child(n+3){--tabcontent-radius-ss:var(--radius-box)}}}}.\32 xl\:tabs-top{--tabs-height:auto;--tabs-direction:row;& .tab{--tab-order:0;--tab-border:0 0 var(--border)0;--tab-radius-ss:min(var(--radius-field),var(--tab-radius-min));--tab-radius-se:min(var(--radius-field),var(--tab-radius-min));--tab-radius-es:0;--tab-radius-ee:0;--tab-paddings:var(--border)var(--tab-p)0 var(--tab-p);--tab-border-colors:#0000 #0000 var(--tab-border-color)#0000;--tab-corner-width:calc(100% + min(var(--radius-field),var(--tab-radius-min))*2);--tab-corner-height:min(var(--radius-field),var(--tab-radius-min));--tab-corner-position:top left,top right;&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked),&:is(label:has(:checked)){--tab-border:var(--border)var(--border)0 var(--border);--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color);--tab-paddings:0 calc(var(--tab-p) - var(--border))var(--border)calc(var(--tab-p) - var(--border));--tab-inset:auto auto 0 auto;--radius-start:radial-gradient(circle at top left,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));--radius-end:radial-gradient(circle at top right,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px))}}&:has(.tab-content){&>.tab:first-child{&:not(.tab-active,[aria-selected=true]){--tab-border-colors:var(--tab-border-color)var(--tab-border-color)#0000 var(--tab-border-color)}}}& .tab-content{--tabcontent-order:1;--tabcontent-margin:calc(-1*var(--border))0 0 0;--tabcontent-radius-ss:0;--tabcontent-radius-se:var(--radius-box);--tabcontent-radius-es:var(--radius-box);--tabcontent-radius-ee:var(--radius-box)}& :checked,& label:has(:checked),& :is(.tab-active,[aria-selected=true]){&+.tab-content{&:first-child,&:nth-child(n+3){--tabcontent-radius-ss:var(--radius-box)}}}}.\32 xl\:tabs-bottom{--tabs-height:auto;--tabs-direction:row;& .tab{--tab-order:1;--tab-border:var(--border)0 0 0;--tab-radius-ss:0;--tab-radius-se:0;--tab-radius-es:min(var(--radius-field),var(--tab-radius-min));--tab-radius-ee:min(var(--radius-field),var(--tab-radius-min));--tab-border-colors:var(--tab-border-color)#0000 #0000 #0000;--tab-paddings:0 var(--tab-p)var(--border)var(--tab-p);--tab-corner-width:calc(100% + min(var(--radius-field),var(--tab-radius-min))*2);--tab-corner-height:min(var(--radius-field),var(--tab-radius-min));--tab-corner-position:top left,top right;&:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&:is(input:checked),&:is(label:has(:checked)){--tab-border:0 var(--border)var(--border)var(--border);--tab-border-colors:#0000 var(--tab-border-color)var(--tab-border-color)var(--tab-border-color);--tab-paddings:var(--border)calc(var(--tab-p) - var(--border))0 calc(var(--tab-p) - var(--border));--tab-inset:0 auto auto auto;--radius-start:radial-gradient(circle at bottom left,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px));--radius-end:radial-gradient(circle at bottom right,#0000 var(--tab-grad),var(--tab-border-color)calc(var(--tab-grad) + .25px),var(--tab-border-color)calc(var(--tab-grad) + var(--border)),var(--tab-bg)calc(var(--tab-grad) + var(--border) + .25px))}}&:has(.tab-content){&>.tab:first-child{&:not(.tab-active,[aria-selected=true]){--tab-border-colors:#0000 var(--tab-border-color)var(--tab-border-color)var(--tab-border-color)}}}& .tab-content{--tabcontent-order:0;--tabcontent-margin:0 0 calc(-1*var(--border))0;--tabcontent-radius-ss:var(--radius-box);--tabcontent-radius-se:var(--radius-box);--tabcontent-radius-es:0;--tabcontent-radius-ee:var(--radius-box)}&>:checked,&>:is(label:has(:checked)),&>:is(.tab-active,[aria-selected=true]){&+.tab-content:not(:nth-child(2)){--tabcontent-radius-es:var(--radius-box)}}}.\32 xl\:tabs-box{background-color:var(--color-base-200);--tabs-box-radius:calc(var(--radius-field) + var(--radius-field) + var(--radius-field));border-radius:calc(var(--radius-field) + min(.25rem,var(--tabs-box-radius)));box-shadow:0 -.5px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 .5px oklch(0% 0 0/calc(var(--depth)*.05))inset;padding:.25rem;& .tab{border-radius:var(--radius-field);border-style:none;&:focus-visible,&:is(label:has(:checked:focus-visible)){outline-offset:2px}}&>:is(.tab-active,[aria-selected=true]):not(.tab-disabled,[disabled]),&>:is(input:checked),&>:is(label:has(:checked)){background-color:var(--tab-bg,var(--color-base-100));box-shadow:0 1px oklch(100% 0 0/calc(var(--depth)*.1))inset,0 1px 1px -1px color-mix(in oklab,var(--color-neutral)calc(var(--depth)*50%),#0000),0 1px 6px -4px color-mix(in oklab,var(--color-neutral)calc(var(--depth)*100%),#0000);@media (forced-colors:active){&{border:1px solid}}}}.\32 xl\:tab-content{--tabcontent-radius-ss:0;--tabcontent-radius-se:0;--tabcontent-radius-es:0;--tabcontent-radius-ee:0;--tabcontent-order:1;width:100%;margin:var(--tabcontent-margin);order:1;order:var(--tabcontent-order);border-color:#0000;border-width:var(--border);border-start-start-radius:var(--tabcontent-radius-ss);border-start-end-radius:var(--tabcontent-radius-se);border-end-end-radius:var(--tabcontent-radius-ee);border-end-start-radius:var(--tabcontent-radius-es);display:none}.\32 xl\:tabs-xs{--tab-height:calc(var(--size-field,.25rem)*6);& :where(.tab){--tab-p:.375rem;--tab-radius-min:calc(.5rem - var(--border));font-size:.75rem}}.\32 xl\:tabs-sm{--tab-height:calc(var(--size-field,.25rem)*8);& :where(.tab){--tab-p:.5rem;--tab-radius-min:calc(.5rem - var(--border));font-size:.875rem}}.\32 xl\:tabs-md{--tab-height:calc(var(--size-field,.25rem)*10);& :where(.tab){--tab-p:.75rem;--tab-radius-min:calc(.75rem - var(--border));font-size:.875rem}}.\32 xl\:tabs-lg{--tab-height:calc(var(--size-field,.25rem)*12);& :where(.tab){--tab-p:1rem;--tab-radius-min:calc(1.5rem - var(--border));font-size:1.125rem}}.\32 xl\:tabs-xl{--tab-height:calc(var(--size-field,.25rem)*14);& :where(.tab){--tab-p:1.25rem;--tab-radius-min:calc(2rem - var(--border));font-size:1.125rem}}}}