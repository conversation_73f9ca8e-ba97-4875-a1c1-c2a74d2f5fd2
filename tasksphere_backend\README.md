# 📝 TaskSphere - Multi-Company Task Management Backend

A Django REST Framework backend for a multi-tenant task management platform that allows multiple companies to manage and track internal tasks efficiently.

## 🚀 Features

### ✅ Implemented Features

- **Multi-tenant Architecture**: Secure, isolated workspaces for multiple companies
- **User Management**: Self-registration with unique 8-digit alphanumeric User IDs
- **Role-based Access Control**: Admin and User roles with appropriate permissions
- **Group Management**: Organize users into departments/teams within companies
- **Task Management**: Create, assign, and track tasks with status updates
- **Task Swap System**: Advanced task swapping with dual approval workflow
- **JWT Authentication**: Secure token-based authentication
- **Notifications**: Real-time notifications for task assignments and updates
- **RESTful API**: Comprehensive REST API with proper error handling

### 🔐 Authentication & Security

- JWT token-based authentication
- Role-based permissions (Admin/User)
- Company code-based registration
- Secure password handling
- CORS configuration for frontend integration

### 👥 User & Group Management

- Self-registration with unique User IDs
- Admin can add/remove users from groups
- Users can be part of multiple groups
- Read-only user profiles for admins

### ✅ Task Management

- Task creation and assignment (Admin only)
- Individual and group task assignment
- Task status tracking (Pending, In Progress, Blocked, Completed)
- Priority levels (Low, Medium, High, Urgent)
- Deadline management
- Task filtering and search

### 🔄 Task Swap System

- Users can request task swaps within groups
- Two-stage approval process:
  1. Admin approval required first
  2. Target user acceptance/rejection
- Automatic task reassignment upon approval
- Comprehensive swap status tracking

## 🛠️ Technology Stack

- **Backend**: Django 5.2, Django REST Framework
- **Authentication**: JWT (djangorestframework-simplejwt)
- **Database**: SQLite (development) - easily configurable for PostgreSQL/MySQL
- **API Documentation**: Comprehensive REST API
- **Admin Interface**: Django Admin for backend management

## 📦 Installation & Setup

### Prerequisites

- Python 3.8+
- pip

### 1. Clone the Repository

```bash
git clone <repository-url>
cd task_sphere
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Database Setup

```bash
# Run migrations
python manage.py migrate

# Create superuser (optional)
python manage.py createsuperuser
```

### 4. Create Sample Data (Optional)

```bash
python create_sample_data.py
```

This creates:
- Sample company (TechCorp Solutions)
- Test users with different roles
- Groups and task assignments
- Sample notifications

### 5. Run the Development Server

```bash
python manage.py runserver
```

The API will be available at `http://127.0.0.1:8000/api/`

## 📚 API Documentation

See [API_DOCUMENTATION.md](API_DOCUMENTATION.md) for comprehensive API documentation including:

- Authentication endpoints
- Company and group management
- Task CRUD operations
- Task swap workflow
- Notification system
- Sample requests and responses

## 🧪 Testing the API

### Sample Credentials (after running create_sample_data.py)

**Company Code**: Check the output of the sample data script

**Test Users**:
- Admin: <EMAIL> - Password: admin123
- Alice: <EMAIL> - Password: user123
- Bob: <EMAIL> - Password: user123
- Carol: <EMAIL> - Password: user123

### Quick Test Flow

1. **Login**:
   ```bash
   POST /api/auth/login/
   {
       "email": "<EMAIL>",
       "password": "admin123"
   }
   ```

2. **Get Profile**:
   ```bash
   GET /api/auth/profile/
   Authorization: Bearer <access_token>
   ```

3. **List Tasks**:
   ```bash
   GET /api/tasks/?company_id=1
   Authorization: Bearer <access_token>
   ```

## 🏗️ Project Structure

```
task_sphere/
├── tasksphere_backend/          # Django project settings
├── authentication/             # Custom user model and auth
├── core/                      # Main business logic
│   ├── models.py             # Company, Group, Task, TaskSwap models
│   ├── views.py              # API views
│   ├── serializers.py        # DRF serializers
│   └── urls.py               # URL routing
├── notifications/             # Notification system
├── create_sample_data.py      # Sample data creation script
├── requirements.txt           # Python dependencies
├── API_DOCUMENTATION.md       # Comprehensive API docs
└── README.md                  # This file
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file for production settings:

```env
SECRET_KEY=your-secret-key
DEBUG=False
ALLOWED_HOSTS=your-domain.com
DATABASE_URL=your-database-url
```

### Database Configuration

The project uses SQLite by default. For production, configure PostgreSQL or MySQL in `settings.py`.

## 🚀 Deployment

### Production Checklist

- [ ] Set `DEBUG=False`
- [ ] Configure production database
- [ ] Set up proper CORS origins
- [ ] Configure static files serving
- [ ] Set up proper logging
- [ ] Use environment variables for secrets

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
1. Check the API documentation
2. Review the sample data and test flow
3. Check Django logs for detailed error messages
4. Open an issue with detailed reproduction steps

## 🔮 Future Enhancements

- Real-time notifications with WebSockets
- File attachments for tasks
- Task comments and activity logs
- Advanced reporting and analytics
- Email notifications
- Calendar integration
- Mobile app support
