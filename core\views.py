from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.db import transaction, models
from django.utils import timezone
from .models import Company, CompanyMembership, Group, GroupMembership, Task, TaskSwap
from authentication.models import User
from .serializers import (
    CompanySerializer, CompanyMembershipSerializer, GroupSerializer,
    GroupMembershipSerializer, AddUserToGroupSerializer, TaskSerializer,
    TaskSwapSerializer, TaskSwapRequestSerializer
)


class IsAdminOrReadOnly(permissions.BasePermission):
    """Custom permission to only allow admins to edit."""

    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return request.user.is_authenticated

        # Check if user is admin in any company
        return CompanyMembership.objects.filter(
            user=request.user,
            role='admin',
            is_active=True
        ).exists()


class CompanyListView(generics.ListAPIView):
    """List companies where user is a member"""
    serializer_class = CompanySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user_companies = CompanyMembership.objects.filter(
            user=self.request.user,
            is_active=True
        ).values_list('company', flat=True)
        return Company.objects.filter(id__in=user_companies, is_active=True)


class CompanyDetailView(generics.RetrieveAPIView):
    """Retrieve company details"""
    serializer_class = CompanySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user_companies = CompanyMembership.objects.filter(
            user=self.request.user,
            is_active=True
        ).values_list('company', flat=True)
        return Company.objects.filter(id__in=user_companies, is_active=True)


class GroupListCreateView(generics.ListCreateAPIView):
    """List and create groups"""
    serializer_class = GroupSerializer
    permission_classes = [IsAdminOrReadOnly]

    def get_queryset(self):
        company_id = self.request.query_params.get('company_id')
        if company_id:
            # Check if user is member of this company
            if CompanyMembership.objects.filter(
                user=self.request.user,
                company_id=company_id,
                is_active=True
            ).exists():
                return Group.objects.filter(company_id=company_id, is_active=True)
        return Group.objects.none()

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class GroupDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, and delete group"""
    serializer_class = GroupSerializer
    permission_classes = [IsAdminOrReadOnly]

    def get_queryset(self):
        user_companies = CompanyMembership.objects.filter(
            user=self.request.user,
            is_active=True
        ).values_list('company', flat=True)
        return Group.objects.filter(company__in=user_companies, is_active=True)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def add_user_to_group(request, group_id):
    """Add user to group by User ID (Admin only)"""
    # Check if user is admin
    if not CompanyMembership.objects.filter(
        user=request.user,
        role='admin',
        is_active=True
    ).exists():
        return Response(
            {'error': 'Only admins can add users to groups'},
            status=status.HTTP_403_FORBIDDEN
        )

    group = get_object_or_404(Group, id=group_id, is_active=True)

    # Check if user is admin of this company
    if not CompanyMembership.objects.filter(
        user=request.user,
        company=group.company,
        role='admin',
        is_active=True
    ).exists():
        return Response(
            {'error': 'You are not an admin of this company'},
            status=status.HTTP_403_FORBIDDEN
        )

    serializer = AddUserToGroupSerializer(data=request.data)
    if serializer.is_valid():
        user_id = serializer.validated_data['user_id']
        user = User.objects.get(user_id=user_id)

        # Check if user is member of the company
        if not CompanyMembership.objects.filter(
            user=user,
            company=group.company,
            is_active=True
        ).exists():
            return Response(
                {'error': 'User is not a member of this company'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if user is already in the group
        if GroupMembership.objects.filter(
            user=user,
            group=group,
            is_active=True
        ).exists():
            return Response(
                {'error': 'User is already a member of this group'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Add user to group
        membership = GroupMembership.objects.create(
            user=user,
            group=group,
            added_by=request.user
        )

        return Response(
            GroupMembershipSerializer(membership).data,
            status=status.HTTP_201_CREATED
        )

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['DELETE'])
@permission_classes([permissions.IsAuthenticated])
def remove_user_from_group(request, group_id, user_id):
    """Remove user from group (Admin only)"""
    # Check if user is admin
    if not CompanyMembership.objects.filter(
        user=request.user,
        role='admin',
        is_active=True
    ).exists():
        return Response(
            {'error': 'Only admins can remove users from groups'},
            status=status.HTTP_403_FORBIDDEN
        )

    group = get_object_or_404(Group, id=group_id, is_active=True)
    user = get_object_or_404(User, user_id=user_id, is_active=True)

    # Check if user is admin of this company
    if not CompanyMembership.objects.filter(
        user=request.user,
        company=group.company,
        role='admin',
        is_active=True
    ).exists():
        return Response(
            {'error': 'You are not an admin of this company'},
            status=status.HTTP_403_FORBIDDEN
        )

    # Remove user from group
    membership = get_object_or_404(
        GroupMembership,
        user=user,
        group=group,
        is_active=True
    )
    membership.is_active = False
    membership.save()

    return Response(
        {'message': 'User removed from group successfully'},
        status=status.HTTP_200_OK
    )


class TaskListCreateView(generics.ListCreateAPIView):
    """List and create tasks"""
    serializer_class = TaskSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        company_id = self.request.query_params.get('company_id')
        status_filter = self.request.query_params.get('status')
        assigned_to_me = self.request.query_params.get('assigned_to_me')

        queryset = Task.objects.none()

        if company_id:
            # Check if user is member of this company
            if CompanyMembership.objects.filter(
                user=self.request.user,
                company_id=company_id,
                is_active=True
            ).exists():
                queryset = Task.objects.filter(company_id=company_id)

                if status_filter:
                    queryset = queryset.filter(status=status_filter)

                if assigned_to_me == 'true':
                    queryset = queryset.filter(assigned_to=self.request.user)

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        # Only admins can create tasks
        if not CompanyMembership.objects.filter(
            user=self.request.user,
            role='admin',
            is_active=True
        ).exists():
            raise permissions.PermissionDenied("Only admins can create tasks")

        serializer.save(created_by=self.request.user)


class TaskDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, and delete task"""
    serializer_class = TaskSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user_companies = CompanyMembership.objects.filter(
            user=self.request.user,
            is_active=True
        ).values_list('company', flat=True)
        return Task.objects.filter(company__in=user_companies)

    def update(self, request, *args, **kwargs):
        task = self.get_object()

        # Check permissions for different updates
        if 'status' in request.data:
            # Users can update status of their own tasks
            if task.assigned_to != request.user:
                # Check if user is admin
                if not CompanyMembership.objects.filter(
                    user=request.user,
                    company=task.company,
                    role='admin',
                    is_active=True
                ).exists():
                    return Response(
                        {'error': 'You can only update status of your own tasks'},
                        status=status.HTTP_403_FORBIDDEN
                    )

            # Set completed_at when status is completed
            if request.data['status'] == 'completed' and task.status != 'completed':
                request.data['completed_at'] = timezone.now()

        # Only admins can update other fields
        admin_only_fields = ['title', 'description', 'assigned_to', 'assigned_group', 'priority', 'deadline']
        if any(field in request.data for field in admin_only_fields):
            if not CompanyMembership.objects.filter(
                user=request.user,
                company=task.company,
                role='admin',
                is_active=True
            ).exists():
                return Response(
                    {'error': 'Only admins can update task details'},
                    status=status.HTTP_403_FORBIDDEN
                )

        return super().update(request, *args, **kwargs)


class TaskSwapListView(generics.ListAPIView):
    """List task swap requests"""
    serializer_class = TaskSwapSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        company_id = self.request.query_params.get('company_id')
        status_filter = self.request.query_params.get('status')

        queryset = TaskSwap.objects.none()

        if company_id:
            # Check if user is member of this company
            if CompanyMembership.objects.filter(
                user=self.request.user,
                company_id=company_id,
                is_active=True
            ).exists():
                # Users see swaps they're involved in, admins see all
                if CompanyMembership.objects.filter(
                    user=self.request.user,
                    company_id=company_id,
                    role='admin',
                    is_active=True
                ).exists():
                    # Admin sees all swaps in the company
                    queryset = TaskSwap.objects.filter(task__company_id=company_id)
                else:
                    # User sees only their swaps
                    queryset = TaskSwap.objects.filter(
                        task__company_id=company_id
                    ).filter(
                        models.Q(requested_by=self.request.user) |
                        models.Q(requested_to=self.request.user)
                    )

                if status_filter:
                    queryset = queryset.filter(status=status_filter)

        return queryset.order_by('-created_at')


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_task_swap(request):
    """Create a task swap request"""
    serializer = TaskSwapRequestSerializer(data=request.data)
    if serializer.is_valid():
        task_id = serializer.validated_data['task_id']
        requested_to_user_id = serializer.validated_data['requested_to_user_id']
        reason = serializer.validated_data.get('reason', '')

        task = Task.objects.get(id=task_id)
        requested_to = User.objects.get(user_id=requested_to_user_id)

        # Check if requesting user is assigned to the task
        if task.assigned_to != request.user:
            return Response(
                {'error': 'You can only swap tasks assigned to you'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Check if both users are in the same group for this task
        if task.assigned_group:
            if not GroupMembership.objects.filter(
                user=requested_to,
                group=task.assigned_group,
                is_active=True
            ).exists():
                return Response(
                    {'error': 'Target user is not in the same group as this task'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Check if there's already a pending swap for this task
        if TaskSwap.objects.filter(
            task=task,
            status__in=['pending_admin', 'pending_user']
        ).exists():
            return Response(
                {'error': 'There is already a pending swap request for this task'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create swap request
        swap = TaskSwap.objects.create(
            task=task,
            requested_by=request.user,
            requested_to=requested_to,
            reason=reason
        )

        return Response(
            TaskSwapSerializer(swap).data,
            status=status.HTTP_201_CREATED
        )

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def admin_approve_swap(request, swap_id):
    """Admin approve task swap request"""
    # Check if user is admin
    if not CompanyMembership.objects.filter(
        user=request.user,
        role='admin',
        is_active=True
    ).exists():
        return Response(
            {'error': 'Only admins can approve swap requests'},
            status=status.HTTP_403_FORBIDDEN
        )

    swap = get_object_or_404(TaskSwap, id=swap_id)

    # Check if user is admin of this company
    if not CompanyMembership.objects.filter(
        user=request.user,
        company=swap.task.company,
        role='admin',
        is_active=True
    ).exists():
        return Response(
            {'error': 'You are not an admin of this company'},
            status=status.HTTP_403_FORBIDDEN
        )

    if swap.status != 'pending_admin':
        return Response(
            {'error': 'This swap request is not pending admin approval'},
            status=status.HTTP_400_BAD_REQUEST
        )

    action = request.data.get('action')  # 'approve' or 'reject'

    if action == 'approve':
        swap.status = 'pending_user'
        swap.admin_approved_by = request.user
        swap.admin_approved_at = timezone.now()
        swap.save()

        return Response(
            {'message': 'Swap request approved by admin'},
            status=status.HTTP_200_OK
        )
    elif action == 'reject':
        swap.status = 'rejected'
        swap.save()

        return Response(
            {'message': 'Swap request rejected by admin'},
            status=status.HTTP_200_OK
        )
    else:
        return Response(
            {'error': 'Invalid action. Use "approve" or "reject"'},
            status=status.HTTP_400_BAD_REQUEST
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def user_respond_swap(request, swap_id):
    """User respond to task swap request"""
    swap = get_object_or_404(TaskSwap, id=swap_id)

    # Check if user is the target of the swap
    if swap.requested_to != request.user:
        return Response(
            {'error': 'You can only respond to swap requests directed to you'},
            status=status.HTTP_403_FORBIDDEN
        )

    if swap.status != 'pending_user':
        return Response(
            {'error': 'This swap request is not pending your response'},
            status=status.HTTP_400_BAD_REQUEST
        )

    action = request.data.get('action')  # 'accept' or 'reject'

    if action == 'accept':
        with transaction.atomic():
            # Swap the task assignment
            task = swap.task
            original_assignee = task.assigned_to
            task.assigned_to = swap.requested_to
            task.save()

            # Update swap status
            swap.status = 'approved'
            swap.user_response = True
            swap.user_responded_at = timezone.now()
            swap.save()

        return Response(
            {'message': 'Task swap completed successfully'},
            status=status.HTTP_200_OK
        )
    elif action == 'reject':
        swap.status = 'rejected'
        swap.user_response = False
        swap.user_responded_at = timezone.now()
        swap.save()

        return Response(
            {'message': 'Swap request rejected'},
            status=status.HTTP_200_OK
        )
    else:
        return Response(
            {'error': 'Invalid action. Use "accept" or "reject"'},
            status=status.HTTP_400_BAD_REQUEST
        )
