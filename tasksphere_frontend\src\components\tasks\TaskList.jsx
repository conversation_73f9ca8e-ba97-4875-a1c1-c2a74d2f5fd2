import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../context/AuthContext';
import { tasksAPI } from '../../services/api';
import {
  CheckSquare,
  Clock,
  AlertCircle,
  Calendar,
  User,
  Filter,
  Plus,
} from 'lucide-react';

const TaskList = () => {
  const { getPrimaryCompany } = useAuth();
  const [statusFilter, setStatusFilter] = useState('');
  const [assignedToMe, setAssignedToMe] = useState(false);
  
  const primaryCompany = getPrimaryCompany();

  const { data: tasksResponse = [], isLoading, error } = useQuery({
    queryKey: ['tasks', primaryCompany?.company_id, statusFilter, assignedToMe],
    queryFn: () => {
      const params = {
        company_id: primaryCompany?.company_id,
      };
      if (statusFilter) params.status = statusFilter;
      if (assignedToMe) params.assigned_to_me = 'true';

      return tasksAPI.getTasks(params);
    },
    enabled: !!primaryCompany?.company_id,
  });

  // Handle paginated response
  const tasks = Array.isArray(tasksResponse) ? tasksResponse : (tasksResponse?.results || []);

  const getStatusBadge = (status) => {
    const statusMap = {
      pending: 'badge-ghost',
      in_progress: 'badge-warning',
      blocked: 'badge-error',
      completed: 'badge-success',
    };
    return statusMap[status] || 'badge-ghost';
  };

  const getPriorityBadge = (priority) => {
    const priorityMap = {
      low: 'badge-info',
      medium: 'badge-warning',
      high: 'badge-error',
      urgent: 'badge-error',
    };
    return priorityMap[priority] || 'badge-ghost';
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'No deadline';
    return new Date(dateString).toLocaleDateString();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-error">
        <AlertCircle className="h-5 w-5" />
        <span>Failed to load tasks. Please try again.</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Tasks</h1>
          <p className="text-base-content/70 mt-1">
            Manage and track your tasks
          </p>
        </div>
        <button className="btn btn-primary">
          <Plus className="h-5 w-5 mr-2" />
          New Task
        </button>
      </div>

      {/* Filters */}
      <div className="card bg-base-100 shadow-sm">
        <div className="card-body p-4">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              <span className="font-medium">Filters:</span>
            </div>
            
            <select
              className="select select-bordered select-sm"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="">All Status</option>
              <option value="pending">Pending</option>
              <option value="in_progress">In Progress</option>
              <option value="blocked">Blocked</option>
              <option value="completed">Completed</option>
            </select>

            <label className="label cursor-pointer">
              <input
                type="checkbox"
                className="checkbox checkbox-sm mr-2"
                checked={assignedToMe}
                onChange={(e) => setAssignedToMe(e.target.checked)}
              />
              <span className="label-text">Assigned to me</span>
            </label>

            <div className="ml-auto">
              <span className="text-sm text-base-content/70">
                {tasks.length} task{tasks.length !== 1 ? 's' : ''}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Tasks Grid */}
      {tasks.length === 0 ? (
        <div className="text-center py-12">
          <CheckSquare className="h-16 w-16 mx-auto mb-4 text-base-content/30" />
          <h3 className="text-lg font-medium mb-2">No tasks found</h3>
          <p className="text-base-content/70">
            {statusFilter || assignedToMe
              ? 'Try adjusting your filters'
              : 'Create your first task to get started'}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tasks.map((task) => (
            <div key={task.id} className="card bg-base-100 shadow-sm hover:shadow-md transition-shadow">
              <div className="card-body">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="card-title text-base">{task.title}</h3>
                  <div className="flex gap-1">
                    <span className={`badge badge-sm ${getStatusBadge(task.status)}`}>
                      {task.status.replace('_', ' ')}
                    </span>
                    <span className={`badge badge-sm ${getPriorityBadge(task.priority)}`}>
                      {task.priority}
                    </span>
                  </div>
                </div>

                <p className="text-sm text-base-content/70 mb-4 line-clamp-3">
                  {task.description}
                </p>

                <div className="space-y-2 text-xs">
                  {task.assigned_to_name && (
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      <span>Assigned to: {task.assigned_to_name}</span>
                    </div>
                  )}
                  
                  {task.assigned_group_name && (
                    <div className="flex items-center gap-2">
                      <CheckSquare className="h-4 w-4" />
                      <span>Group: {task.assigned_group_name}</span>
                    </div>
                  )}

                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span className={task.is_overdue ? 'text-error' : ''}>
                      Due: {formatDate(task.deadline)}
                      {task.is_overdue && ' (Overdue)'}
                    </span>
                  </div>

                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span>Created: {formatDate(task.created_at)}</span>
                  </div>
                </div>

                <div className="card-actions justify-end mt-4">
                  <button className="btn btn-ghost btn-sm">View</button>
                  <button className="btn btn-primary btn-sm">Edit</button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default TaskList;
