from rest_framework import serializers
from django.db import transaction
from .models import Company, CompanyMembership, Group, GroupMembership, Task, TaskSwap
from authentication.models import User


class CompanySerializer(serializers.ModelSerializer):
    """Serializer for Company model"""
    member_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Company
        fields = ['id', 'name', 'company_code', 'description', 'created_at', 
                 'updated_at', 'is_active', 'member_count']
        read_only_fields = ['company_code', 'created_at', 'updated_at']

    def get_member_count(self, obj):
        return CompanyMembership.objects.filter(company=obj, is_active=True).count()


class CompanyMembershipSerializer(serializers.ModelSerializer):
    """Serializer for Company Membership"""
    user_details = serializers.SerializerMethodField()
    
    class Meta:
        model = CompanyMembership
        fields = ['id', 'user', 'company', 'role', 'joined_at', 'is_active', 'user_details']
        read_only_fields = ['joined_at']

    def get_user_details(self, obj):
        return {
            'user_id': obj.user.user_id,
            'email': obj.user.email,
            'first_name': obj.user.first_name,
            'last_name': obj.user.last_name,
        }


class GroupSerializer(serializers.ModelSerializer):
    """Serializer for Group model"""
    member_count = serializers.SerializerMethodField()
    created_by_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Group
        fields = ['id', 'name', 'description', 'company', 'created_by', 
                 'created_at', 'updated_at', 'is_active', 'member_count', 'created_by_name']
        read_only_fields = ['created_by', 'created_at', 'updated_at']

    def get_member_count(self, obj):
        return GroupMembership.objects.filter(group=obj, is_active=True).count()
    
    def get_created_by_name(self, obj):
        return f"{obj.created_by.first_name} {obj.created_by.last_name}"


class GroupMembershipSerializer(serializers.ModelSerializer):
    """Serializer for Group Membership"""
    user_details = serializers.SerializerMethodField()
    added_by_name = serializers.SerializerMethodField()
    
    class Meta:
        model = GroupMembership
        fields = ['id', 'user', 'group', 'added_by', 'joined_at', 
                 'is_active', 'user_details', 'added_by_name']
        read_only_fields = ['added_by', 'joined_at']

    def get_user_details(self, obj):
        return {
            'user_id': obj.user.user_id,
            'email': obj.user.email,
            'first_name': obj.user.first_name,
            'last_name': obj.user.last_name,
        }
    
    def get_added_by_name(self, obj):
        return f"{obj.added_by.first_name} {obj.added_by.last_name}"


class AddUserToGroupSerializer(serializers.Serializer):
    """Serializer for adding user to group by User ID"""
    user_id = serializers.CharField(max_length=8)
    
    def validate_user_id(self, value):
        try:
            user = User.objects.get(user_id=value, is_active=True)
        except User.DoesNotExist:
            raise serializers.ValidationError("User with this ID does not exist.")
        return value


class TaskSerializer(serializers.ModelSerializer):
    """Serializer for Task model"""
    created_by_name = serializers.SerializerMethodField()
    assigned_to_name = serializers.SerializerMethodField()
    assigned_group_name = serializers.SerializerMethodField()
    is_overdue = serializers.SerializerMethodField()
    
    class Meta:
        model = Task
        fields = ['id', 'title', 'description', 'company', 'created_by', 
                 'assigned_to', 'assigned_group', 'status', 'priority', 
                 'deadline', 'created_at', 'updated_at', 'completed_at',
                 'created_by_name', 'assigned_to_name', 'assigned_group_name', 'is_overdue']
        read_only_fields = ['created_by', 'created_at', 'updated_at', 'completed_at']

    def get_created_by_name(self, obj):
        return f"{obj.created_by.first_name} {obj.created_by.last_name}"
    
    def get_assigned_to_name(self, obj):
        if obj.assigned_to:
            return f"{obj.assigned_to.first_name} {obj.assigned_to.last_name}"
        return None
    
    def get_assigned_group_name(self, obj):
        if obj.assigned_group:
            return obj.assigned_group.name
        return None
    
    def get_is_overdue(self, obj):
        if obj.deadline and obj.status != 'completed':
            from django.utils import timezone
            return timezone.now() > obj.deadline
        return False


class TaskSwapSerializer(serializers.ModelSerializer):
    """Serializer for Task Swap"""
    task_title = serializers.SerializerMethodField()
    requested_by_name = serializers.SerializerMethodField()
    requested_to_name = serializers.SerializerMethodField()
    admin_approved_by_name = serializers.SerializerMethodField()
    
    class Meta:
        model = TaskSwap
        fields = ['id', 'task', 'requested_by', 'requested_to', 'status',
                 'admin_approved_by', 'admin_approved_at', 'user_response',
                 'user_responded_at', 'reason', 'created_at', 'updated_at',
                 'task_title', 'requested_by_name', 'requested_to_name', 'admin_approved_by_name']
        read_only_fields = ['requested_by', 'created_at', 'updated_at']

    def get_task_title(self, obj):
        return obj.task.title
    
    def get_requested_by_name(self, obj):
        return f"{obj.requested_by.first_name} {obj.requested_by.last_name}"
    
    def get_requested_to_name(self, obj):
        return f"{obj.requested_to.first_name} {obj.requested_to.last_name}"
    
    def get_admin_approved_by_name(self, obj):
        if obj.admin_approved_by:
            return f"{obj.admin_approved_by.first_name} {obj.admin_approved_by.last_name}"
        return None


class TaskSwapRequestSerializer(serializers.Serializer):
    """Serializer for creating task swap request"""
    task_id = serializers.IntegerField()
    requested_to_user_id = serializers.CharField(max_length=8)
    reason = serializers.CharField(required=False, allow_blank=True)
    
    def validate_task_id(self, value):
        try:
            task = Task.objects.get(id=value)
        except Task.DoesNotExist:
            raise serializers.ValidationError("Task does not exist.")
        return value
    
    def validate_requested_to_user_id(self, value):
        try:
            user = User.objects.get(user_id=value, is_active=True)
        except User.DoesNotExist:
            raise serializers.ValidationError("User with this ID does not exist.")
        return value
